import React, { useEffect, useState } from "react";
import dynamic from "next/dynamic";
import { Spinner } from "../ui/progressiveLoder";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title, CardContent, Card } from "../ui/card";
import { CategoryWiseProgressType, UserStatistics } from "@/types";
import { ResponsiveContainer } from "recharts";

// Dynamically import ReactApex<PERSON><PERSON> to avoid SSR issues
const ReactApexChart = dynamic(() => import("react-apexcharts"), {
  ssr: false,
});

interface SubjectProgressInterface {
  ScoreDetails: UserStatistics[];
  courseId: string | null;
}

const ScoreCardGraph = ({
  ScoreDetails,
  courseId,
}: SubjectProgressInterface) => {
  const [scoreData, setScoreData] = useState<CategoryWiseProgressType[]>([]);
  const [chartOptions, setChartOptions] = useState<any | null>(null);
  const [chartSeries, setChartSeries] = useState<number[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  // Define a consistent color palette
  const generateConsistentColors = (count: number) => {
    const baseColors = [
      "#008FFB",
      "#00E396",
      "#FEB019",
      "#FF4560",
      "#775DD0",
      "#546E7A",
      "#26a69a",
      "#D10CE8",
    ];
    return baseColors.slice(0, count);
  };

  useEffect(() => {
    // Process data for the chart
    const newScoreData = ScoreDetails?.slice(0, 5).map((item: any) => ({
      name: courseId === null ? item.course_name : item.resource_name,
      value: item.totalMarks,
    }));

    const colors = generateConsistentColors(newScoreData.length);

    // Prepare ApexCharts radial bar chart options and series
    const series = newScoreData.map((item) => item.value);
    const options = {
      chart: {
        height: 200,
        type: "radialBar",
      },
      plotOptions: {
        radialBar: {
          offsetY: 0,
          startAngle: 0,
          endAngle: 360,
          hollow: {
            margin: 5,
            size: "10%",
            background: "transparent",
          },
          dataLabels: {
            name: {
              show: false,
            },
            value: {
              show: false,
            },
          },
          track: {
            background: "#f2f2f2",
          },
        },
      },
      colors: colors, // Use the consistent colors
      labels: newScoreData.map((item) => item.name),
      tooltip: {
        enabled: true,
        y: {
          formatter: function (value: number) {
            return `${value} points`;
          },
        },
      },
    };

    setScoreData(newScoreData);
    setChartOptions(options);
    setChartSeries(series);
    setIsLoading(false);
  }, [ScoreDetails, courseId]);

  return (
    <>
      {!isLoading ? (
        <Card className="bg-white border border-gray-200 rounded-lg w-full shadow-md">
          <div className="bg-blue-600 h-2 w-full rounded-t-lg"></div>
          <CardHeader className="bg-blue-100">
            <CardTitle className="text-lg font-semibold text-blue-700">
              Score
            </CardTitle>
          </CardHeader>
          <CardContent>
            {scoreData.length > 0 ? (
              <ResponsiveContainer width="100%" height={280}>
                <div className="flex flex-col items-center justify-center mt-2">
                  <ReactApexChart
                    options={chartOptions}
                    series={chartSeries}
                    type="radialBar"
                    height={180}
                  />
                  <div className="items-center pl-12">
                    {scoreData.map((item, index) => (
                      <div key={index} className="flex items-left space-x-2">
                        <span
                          className="w-3 h-3"
                          style={{
                            backgroundColor: generateConsistentColors(
                              scoreData.length
                            )[index],
                          }}
                        ></span>
                        <span className="text-xs font-normal text-gray-600 pb-1">
                          {item.name}: {item.value} points
                        </span>
                      </div>
                    ))}
                  </div>
                </div>
              </ResponsiveContainer>
            ) : (
              <div className="flex flex-col items-center justify-center h-[290px]">
                <p className="text-gray-500 text-lg font-medium pb-10">
                  No data available!
                </p>
              </div>
            )}
          </CardContent>
        </Card>
      ) : (
        <Spinner />
      )}
    </>
  );
};

export default ScoreCardGraph;
