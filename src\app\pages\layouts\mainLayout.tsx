"use client";
import { type Metadata } from "next";
import React, {
  createContext,
  useContext,
  useMemo,
  useState,
  useEffect,
  Suspense,
} from "react";
import { Sidebar } from "../../../components/ui/navbar";
import { useRouter } from "next/router";
import { Menu } from "@/components/ui/menu";
import { Skeleton } from "@/components/ui/skelton";
import { useMediaQuery } from "react-responsive";
import { Modal } from "@/components/ui/modal";
import LoginAlert from "./loginAlert";
import { KEYS } from "@/lib/keys";
import AppHeader from "@/components/app-header";
import { CourseData } from "@/types";

interface PropsWithChildren {
  children: React.ReactNode;
  titleText: string;
  showMenuBar?: boolean;
}
export const AuthContext = createContext({ session: null, user: null });

export const AuthProvider = ({
  ...props
}: {
  children?: React.ReactNode;
}): React.JSX.Element => {
  const [session] = useState(null);
  const [user] = useState(null);

  const value = useMemo(() => {
    return {
      session,
      user,
    };
  }, [session, user]);

  return <AuthContext.Provider value={value} {...props} />;
};

const MainLayout = ({
  children,
  titleText,
  showMenuBar,
}: PropsWithChildren): React.JSX.Element => {
  const [showMenu, setShowMenu] = useState<boolean>(true);
  const [sidebarData, setSidebarData] = useState<any[]>([]);
  const [titleName, setTitle] = useState("");
  const isMobileOrTablet = useMediaQuery({ maxWidth: 1024 });
  const showMenuBarData = showMenuBar === undefined ? true : showMenuBar;
  const [openlogin, setOpenLogin] = useState(false);
  const [selectedCourse, setSelectedCourse] = useState<CourseData | null>(null);
  const [courseId, setSelectedCourseId] = useState<string>("");

  useEffect(() => {
    document.removeEventListener("click", handleClickOutside);
    setTitle(titleText);
    handleResize();
  }, [titleText, setTitle]);

  const handleResize = () => {
    if (window.innerWidth < 1024) {
      setShowMenu(false);
    } else {
      setShowMenu(true);
    }
  };

  useEffect(() => {
    if (typeof window !== "undefined") {
      window.addEventListener("resize", handleResize);
      return () => {
        window.removeEventListener("resize", handleResize);
      };
    }
  }, []);

  useEffect(() => {
    const checkAccessToken = () => {
      const token = localStorage.getItem(KEYS.ACCESS_TOKEN);
      if (!token) {
        setOpenLogin(true);
      } else {
        setOpenLogin(false);
      }
    };

    setTimeout(() => {
      checkAccessToken();
    }, 2000); 

    const handleStorageChange = (event: StorageEvent) => {
      if (event.key === KEYS.ACCESS_TOKEN) {
        setTimeout(() => {
          checkAccessToken();
        }, 2000); 
      }
    };

    window.addEventListener("storage", handleStorageChange);

    return () => {
      window.removeEventListener("storage", handleStorageChange);
    };
  }, []);

  const handleClickOutside = (event: MouseEvent) => {
    const sidebar = document.getElementById("sidebar");
    if (
      isMobileOrTablet &&
      sidebar &&
      !sidebar.contains(event.target as Node)
    ) {
      setShowMenu(false);
    }
  };

  const closeModal = (): void => {
    setOpenLogin(false);
  };

  const handleCourseChange = (course: CourseData) => {
    setSelectedCourse(course);
    setSelectedCourseId(course.course_id);
  };

  return (
    <>
      <AppHeader
        title={titleName}
        onCourseChange={handleCourseChange}
      ></AppHeader>
      <div className="flex">
        <div className="w-full min-h-screen flex flex-col bg-[var(--color-background)] font-custom">
          <div className="h-full p-6 text-[var(--color-font-color)]" >
            <Suspense
              fallback={
                <Skeleton className="w-[100px] h-[20px] rounded-full" />
              }
            >
              {children}
            </Suspense>
          </div>
          <footer className="h-16 border-t border-white/2  backdrop-blur-xl px-4 flex items-center justify-center transition-all duration-500 bg-[var(--color-footer-bg)]" > 
            <p className="text-sm text-[var(--color-footer-text)]">
              © {new Date().getFullYear()} Citrus Informatics (India) Pvt Ltd. All
              rights reserved.
            </p>
          </footer>
        </div>
        <Modal
          title=""
          header=""
          openDialog={openlogin}
          closeDialog={closeModal}
          type="max-w-lg"
        >
          <LoginAlert></LoginAlert>
        </Modal>
      </div>
    </>
  );
};

export default MainLayout;
