export const views = {
  profileDetails: "v_person",
  signup: "signup",
  sendResetPasswordLink: "recover",
  courseDetails: "v_course_details",
  currentAffairs: "v_view_bulletin_board",
  submitAnswers: "v_submit_answers",
  userOrgnizationData: "v_user_orgs",
};
export const rpc = {
  getSectionDetails: "fn_fetch_section_details",
  getQuizesOfCourse: "get_quizes_of_course",
  getAttemptedQuizesOfUser: "get_attempted_quizes_of_user",
  getQuestionOfQuiz: "get_questions_of_quiz",
  submitExam: "submit_quiz_answers",
  startExam: "start_quiz",
  getQuizRightAnswers: "fn_get_quiz_right_answers",
  getSubscriptionReport: "get_subscription_purchase_report",
  updateProfile: "update_profile",
  fetchRankListForCourse: "fetch_rank_list_for_course",
  fetchRankListForQuiz: "fetch_rank_list_for_quiz",
  calculateQuizGrade: "fn_evaluate_quiz",
  resultAnalysis: "get_attempted_quizes_of_user",
  getCategoryHierarchyEnrolled: "get_category_hierarchy_enrolled",
  getComments: "fn_fetch_comments_of_instance",
  getCheckpoints: "get_check_point_by_course_module_id",
  startQuiz: "start_checkpoint_quiz",
  endQuiz: "fn_submit_checkpoint_quiz",
  getSubscription: "get_subscription_plans_for_user",
  addSubscription: "add_subscription_plan_for_user",
  getPlanPurchaseStatus: "get_user_purchase_plan_status",
  getResourcesListByPlan: "get_course_resource_list_by_plan",
  getCourseDetails: "fn_get_course_details",
  getAllResoures: "get_course_resources_details",
  insertNotification: "insert_push_notification",
  getCourseResourceDetails: "fn_get_course_resources_details",
  // addComments: "add_comment",
  // addComments: "ms_fn_add_comment",
  addComments: "fn_post_comment",
  // userStatistics: "fn_get_user_statistics",
  userStatistics: "fn_get_user_course_statistics",
  recentActivities: "fn_get_recent_activities",
  courseCompletionProgress: "fn_get_user_course_progress",
  getExamPerformance: "fn_get_exam_performance_category_wise",
  getCategoryPerformanceAllCourses: "fn_get_category_summary_all_courses",
  getAllNotifiation: "fn_get_user_notifications_list",
  setCourseProgress: "set_course_progress",
  getCourseProgress: "get_course_progress",
  // getMarkPerResource : "fn_get_coursewise_user_statistics",
  userCourseStatistics: "fn_get_coursewise_user_statistics",
  getAllCoursesData: "fn_get_user_course_all_stats",
  assignOrganization: "add_org_claim_role_to_users",
  updateDocumentProgress: "fn_update_file_view_progress",
  getCheckpointSessionReport: "get_checkpoint_sessions_report",
  skipResource: "fn_skip_resource",
  getConfigSettings: "fn_get_app_dashboard_config",
  getLiveClassDetails: "fn_get_live_class_details",
  getCustomBrandingDetails: "fn_get_custom_branding_details",
  logUserActivity: "fn_record_user_activity",
};

export const functions = {
  sendEmailNotification:
    process.env.NEXT_PUBLIC_SUPABASE_URL +
    "/functions/v1/sendUsersignupEmailAlert",
};
