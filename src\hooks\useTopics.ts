import { supabase } from "../lib/client";
import { rpc } from "@/lib/apiConfig";
import { TopicsData, TopicsRequest, ErrorType } from "@/types";

interface UseTopicsReturn {
  getTopicsData: (queryParams: TopicsRequest) => Promise<TopicsData[]>;
}

export const UseTopics = (): UseTopicsReturn => {
  async function getTopicsData(params: TopicsRequest): Promise<TopicsData[]> {
    try {
      const { data, error } = (await supabase.rpc<string, null>(
        rpc.getCategoryHierarchyEnrolled,
        params
      )) as { data: TopicsData[]; error: ErrorType | null };
      if (error) {
        throw new Error(error.details);
      }
      return data as TopicsData[];
    } catch (error) {
      console.error("Error:", error);
      throw error;
    }
  }

  return {
    getTopicsData,
  };
};
