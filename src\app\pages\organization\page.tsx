"use client";
import React from "react";
import "../../../styles/main.css";
import MainLayout from "../layouts/mainLayout";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";

import { But<PERSON> } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { OrganizationFormSchema } from "@/schema/schema";
import { UserOrganizationReturn } from "@/types";
import { useRouter } from "next/navigation";
import { KEYS } from "@/lib/keys";
import { ORG_DETAILS_KEY } from "@/lib/utils";
import { useTranslation } from "next-i18next";
interface OrgData {
  org_id: string;
  org_name: string;
}
export default function Organization(): React.JSX.Element {
  const { t } = useTranslation("common");
  const orgData = ORG_DETAILS_KEY as string;
  const organizationData = JSON.parse(orgData) as UserOrganizationReturn[];
  const router = useRouter();
  const form = useForm<z.infer<typeof OrganizationFormSchema>>({
    resolver: zodResolver(OrganizationFormSchema),
    defaultValues: {
      organization: [], // Ensure default value is an empty array
    },
  });

  const onSubmit = (data: z.infer<typeof OrganizationFormSchema>) => {
    localStorage.setItem(KEYS.ORG_ID, data.organization[0].org_id);
    localStorage.setItem(KEYS.ORG_NAME, data.organization[0].org_name);
    router.push("/pages/topics");
  };

  return (
    <MainLayout titleText="">
      <div className="text-base font-semibold p-3">
        {" "}
        {t("Select Organization")}
      </div>
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
          <FormField
            control={form.control}
            name="organization"
            render={() => (
              <FormItem>
                {organizationData?.map((item) => (
                  <FormField
                    key={item.org_id}
                    control={form.control}
                    name="organization"
                    render={({ field }) => {
                      const fieldValue = field.value || [];

                      return (
                        <FormItem
                          key={item.org_id}
                          className="flex flex-row sm:w-full md:w-5/6 lg:w-5/6 items-start space-x-3 space-y-0 border border border-[#423338] rounded-lg hover:border-[#00AFBB] p-2 mb-4"
                        >
                          <FormLabel className="w-[98%] font-normal text-base text-[#423338]">
                            {item.org_name}
                          </FormLabel>
                          <FormControl className="rounded focus:ring-[#00AFBB] dark:focus:ring-[#00AFBB] dark:ring-offset-[#00AFBB] focus:ring-2 dark:bg-[#00AFBB] dark:border-[#00AFBB]">
                            <Checkbox
                              checked={field.value.some(
                                (selectedItem: OrgData) =>
                                  selectedItem.org_id === item.org_id
                              )}
                              onCheckedChange={(checked) => {
                                const newValue = checked
                                  ? [...field.value, item]
                                  : field.value.filter(
                                      (selectedItem: OrgData) =>
                                        selectedItem.org_id !== item.org_id
                                    );

                                field.onChange(newValue);
                              }}
                            />
                          </FormControl>
                        </FormItem>
                      );
                    }}
                  />
                ))}
                <FormMessage />
              </FormItem>
            )}
          />
          <div className="sm:w-full md:w-5/6 lg:w-5/6 grid justify-center p-10">
            <Button type="submit" className="space-x-2  rounded-lg w-96 h-12">
              <span className="text-white text-base font-semibold">
                {t("Continue")}
              </span>
            </Button>
          </div>
        </form>
      </Form>
    </MainLayout>
  );
}
