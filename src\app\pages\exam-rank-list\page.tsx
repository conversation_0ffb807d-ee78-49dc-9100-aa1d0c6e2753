"use client";
import React, { useEffect, useState } from "react";
import MainLayout from "../layouts/mainLayout";
import "../../../styles/main.css";
import { Card } from "@/components/ui/card";
import Image from "next/image";
import { BADGE_FIRST, BADGE_SECOND, BADGE_THIRD } from "@/lib/constants";
import { Circle, Square } from "lucide-react";
import { useExam } from "@/hooks/useExam";
import { useRouter, useSearchParams } from "next/navigation";
import {
  ErrorCatch,
  ExamRankListRequest,
  InnerItem,
  LoginUserData,
  RankListData,
  ToastType,
} from "@/types";
import { useToast } from "@/components/ui/use-toast";
import { Separator } from "@/components/ui/separator";
import { KEYS } from "@/lib/keys";
import { USER_DATA } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { ERROR_MESSAGES } from "@/lib/messages";
import NextBreadcrumb from "@/components/breadcrumb";
import getBreadCrumbItems from "@/hooks/useBreadcrumbs";

export default function ExamRankList(): React.JSX.Element {
  const { toast } = useToast() as unknown as ToastType;
  const { getExamRankList } = useExam();
  const router = useRouter();
  const searchParams = useSearchParams();
  const quizId = searchParams?.get("quiz_id") as string;
  const tab = searchParams?.get("tab") as string;
  const courseId = searchParams?.get("course_id") as string;
  const [topThree, setTopThree] = useState<RankListData[]>([]);
  const [remainingRankList, setRemainingRankList] = useState<RankListData[]>(
    []
  );
  const [breadcrumbItems, setBreadcrumbItems] = useState<InnerItem[]>([]);
  const [currentUserData, setCurrentUserData] = useState<
    RankListData | undefined
  >(undefined);

  const orgID = localStorage.getItem(KEYS.ORG_ID);

  useEffect(() => {
    fetchExamRankList();
    setBreadcrumbItems(
      getBreadCrumbItems("Exam Rank List", {
        course_id: courseId
      })
    );
  }, []);

  const fetchExamRankList = async (): Promise<void> => {
    const params: ExamRankListRequest = {
      org_id: orgID as string,
      quiz_id: quizId,
      quiz_type_filter: "Main",
    };
    try {
      const response = await getExamRankList(params);
      if (response?.status === "success") {
        if (response.rank_list != null) {
          const sortedRankList = response?.rank_list.sort(
            (a, b) => b.total_sum_grades - a.total_sum_grades
          );

          setTopThree(sortedRankList.slice(0, 3));
          setRemainingRankList(sortedRankList.slice(3, 13)); // Display next 10 performers
          if (USER_DATA !== null) {
            const userInfo = JSON.parse(USER_DATA) as LoginUserData;
            const userRank = sortedRankList.find(
              (rank) => rank.user_id === userInfo.id
            );
            setCurrentUserData(userRank);
          }
        }
      }
    } catch (error) {
      const err = error as ErrorCatch;
      toast({
        variant: "destructive",
         title: ERROR_MESSAGES.error,
        description: err?.message,
      });
    }
  };

  const handleCancel = (): void => {
    router.push(`/pages/exams-list?course_id=${courseId}&tab=${tab}`);
  };
  return (
    <MainLayout titleText="">
      <NextBreadcrumb
        items={breadcrumbItems}
        separator={<span> | </span>}
        containerClasses="flex py-5"
        listClasses="hover:underline mx-2 font-bold"
        capitalizeLinks
      />
      {topThree.length > 0 ? (
        <div className="flex flex-col gap-8">
          <Card className="bg-[#F6E5D3] rounded-lg mt-2">
            <div className="grid gap-4 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-3 p-4 gap-8 pr-6">
              {topThree.map((item, index) => {
                let badgeImage = null;
                if (index === 0) {
                  badgeImage = BADGE_FIRST;
                } else if (index === 1) {
                  badgeImage = BADGE_SECOND;
                } else if (index === 2) {
                  badgeImage = BADGE_THIRD;
                }
                return (
                  <div key={index} className="relative">
                    <Card className="p-4">
                      <div className="grid grid-cols-6 gap-4">
                        <div className="relative w-16 h-16 items-center">
                          <Image
                            src={item.avatar_url}
                            alt="Card Image"
                            width={64}
                            height={64}
                            className="rounded-lg"
                          />
                          <div className="absolute bottom-0 right-0 translate-x-1/2 translate-y-1/2 flex items-center justify-center">
                            <Circle className="fill-[#00AFBB] text-[#00AFBB]" />
                            <span className="text-white absolute">
                              {index + 1}
                            </span>
                          </div>
                        </div>
                        <div className="ps-6 col-start-2 col-span-4 gap-4">
                          <div className="grid grid-rows-2 grid-flow-col gap-4">
                            <div className="flex justify-between">
                              <div>{item.user_name}</div>
                              <div className="text-[#00AFBB]">
                                <b>{item.total_sum_grades}</b>
                              </div>
                            </div>
                            <div className="grid grid-cols-3 gap-4">
                              <div className="flex flex-row items-center">
                                <Square className="bg-[#00A642] text-[#00A642] w-4 h-4" />
                                <p className="text-sm pl-1">
                                  {item.correct_answers_count}
                                </p>
                              </div>
                              <div className="flex flex-row items-center">
                                <Square className="bg-[#FF2B2B] text-[#FF2B2B] w-4 h-4" />
                                <span className="text-sm pl-1">
                                  {item.wrong_answers_count}
                                </span>
                              </div>
                              <div className="flex flex-row items-center">
                                <Square className="bg-[#FFC107] text-[#FFC107] w-4 h-4" />
                                <span className="text-sm pl-1">
                                  {item.skipped_answers_count}
                                </span>
                              </div>
                            </div>
                          </div>
                        </div>
                        {badgeImage && (
                          <div className="absolute top-6 right-badge z-10">
                            <Image
                              src={badgeImage}
                              alt="Badge Image"
                              width={45}
                              height={45}
                            />
                          </div>
                        )}
                      </div>
                    </Card>
                  </div>
                );
              })}
            </div>
          </Card>
          <div className="grid grid-cols-3 gap-4 justify-items-center">
            <div className="flex flex-row items-center">
              <Square className="bg-[#00A642] text-[#00A642] w-4 h-4" />
              <p className="text-sm pl-1">Correct</p>
            </div>
            <div className="flex flex-row items-center">
              <Square className="bg-[#FF2B2B] text-[#FF2B2B] w-4 h-4" />
              <span className="text-sm pl-1">Wrong</span>
            </div>
            <div className="flex flex-row items-center">
              <Square className="bg-[#FFC107] text-[#FFC107] w-4 h-4" />
              <span className="text-sm pl-1">Skipped</span>
            </div>
          </div>
          {currentUserData && (
            <Card className="p-4 bg-[#423338] text-white">
              <div className="grid grid-cols-6 gap-4">
                <div className="relative w-16 h-16 items-center">
                  <Image
                    src={currentUserData?.avatar_url}
                    alt="Card Image"
                    width={64}
                    height={64}
                    className="rounded-lg"
                  />
                </div>
                <div className="ps-6 col-start-2 col-span-4 gap-4">
                  <div className="grid grid-rows-2 grid-flow-col gap-4">
                    <div className="flex justify-between">
                      <div>{currentUserData.user_name}</div>
                    </div>
                    <div className="grid grid-cols-4 gap-4">
                      <div className="flex flex-row items-center text-[#FB8500]">
                        <p>Mark :</p>
                        <b>{currentUserData.total_sum_grades}</b>
                      </div>
                      <div className="flex flex-row items-center">
                        <Square className="bg-[#00A642] text-[#00A642] w-4 h-4" />
                        <p className="text-sm pl-1">
                          {currentUserData.correct_answers_count}
                        </p>
                      </div>
                      <div className="flex flex-row items-center">
                        <Square className="bg-[#FF2B2B] text-[#FF2B2B] w-4 h-4" />
                        <span className="text-sm pl-1">
                          {currentUserData.wrong_answers_count}
                        </span>
                      </div>
                      <div className="flex flex-row items-center">
                        <Square className="bg-[#FFC107] text-[#FFC107] w-4 h-4" />
                        <span className="text-sm pl-1">
                          {currentUserData.skipped_answers_count}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </Card>
          )}

          {remainingRankList?.length > 0 && (
            <div className="flex flex-col gap-4">
              <h3>Top 10 Performers</h3>
              {remainingRankList?.map((item, index) => (
                <div key={index}>
                  <Card className="p-4 bg-white rounded-lg border-none shadow-none">
                    <div className="grid w-full grid-cols-4 gap-4">
                      <div className="col-span-1 relative">
                        <Image
                          src={item.avatar_url}
                          alt="Card Image"
                          width={60}
                          height={60}
                          className="rounded-full"
                        />
                        <div className="absolute z-10 left-12 bottom-0.5">
                          <Square className="fill-[#00AFBB] text-[#00AFBB]" />
                          <span className="text-white absolute left-1.5 bottom-rank">
                            {index + 4}
                          </span>
                        </div>
                      </div>
                      <div className="col-span-3">
                        <div className="flex flex-col">
                          <div className="grid grid-cols-2 pt-3">
                            <div>{item.user_name}</div>
                          </div>
                          <div className="grid grid-cols-2 pt-3">
                            <div className="text-[#FB8500]">
                              Mark: <b>{item.total_sum_grades}</b>
                            </div>
                            <div className="text-[#FB8500]">
                              <div className="grid grid-cols-3 gap-4">
                                <div className="flex flex-row items-center">
                                  <Square className="bg-[#00A642] text-[#00A642] w-4 h-4" />
                                  <p className="text-sm pl-1">
                                    {item.correct_answers_count}
                                  </p>
                                </div>
                                <div className="flex flex-row items-center">
                                  <Square className="bg-[#FF2B2B] text-[#FF2B2B] w-4 h-4" />
                                  <span className="text-sm pl-1">
                                    {item.wrong_answers_count}
                                  </span>
                                </div>
                                <div className="flex flex-row items-center">
                                  <Square className="bg-[#FFC107] text-[#FFC107] w-4 h-4" />
                                  <span className="text-sm pl-1">
                                    {item.skipped_answers_count}
                                  </span>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </Card>
                  <Separator />
                </div>
              ))}
            </div>
          )}
        </div>
      ) : (
        <p className="text-center mt-4">No Rank List Found</p>
      )}
      <div className="flex justify-end mt-4 ">
        <Button
          variant="outline"
          className="rounded-md"
          onClick={handleCancel}
        >
          Back
        </Button>
      </div>
    </MainLayout>
  );
}
