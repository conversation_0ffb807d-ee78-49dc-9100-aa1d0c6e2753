"use client";
import React, { useEffect, useState } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { getLocalStorageItem } from "@/lib/utils";
import { KEYS } from "@/lib/keys";
import MainLayout from "../layouts/mainLayout";
import { Card } from "@/components/ui/card";
import { DocumentViewer } from "react-documents";
import {
  CheckCircle,
  MessageCircle,
  MessageSquarePlus,
  PresentationIcon,
  ThumbsUp,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import NextBreadcrumb from "@/components/breadcrumb";
import {
  CheckPoint,
  CommentsResponse,
  ErrorCatch,
  InnerItem,
  ToastType,
  UpdateDocumentRequest,
} from "@/types";
import getBreadCrumbItems from "@/hooks/useBreadcrumbs";
import { Spinner } from "@/components/ui/progressiveLoder";
import { useCourse } from "@/hooks/useCourse";
import { useToast } from "@/components/ui/use-toast";
import { SUCCESS_MESSAGES } from "@/lib/messages";
import CommentsModal from "@/components/comment-modal";
import { Modal } from "@/components/ui/modal";
import useComments from "@/hooks/useComments";
import CommentsSection from "@/components/comments/comments";
import { UseLogClass } from "@/hooks/useLog";
import { UUID } from "@/lib/constants";
import { useTranslation } from "next-i18next";
export default function StudyMaterialView() {
  const { t } = useTranslation("common");
  const router = useRouter();
  const searchParams = useSearchParams();
  const getResourceData = getLocalStorageItem(KEYS.RESOURCE_DATA);
  const parsedData = getResourceData ? JSON.parse(getResourceData) : {};
  const isGeneral = searchParams?.get("isGeneral");
  const pdfUrl = getLocalStorageItem(KEYS.PDF_URL);
  const pdfName = getLocalStorageItem(KEYS.PDF_NAME);
  const fileUrl =
    isGeneral === "true" ? pdfUrl : parsedData.url ? parsedData.url : pdfUrl;
  const fileName =
    isGeneral === "true"
      ? pdfName
      : parsedData.name
      ? parsedData.name
      : pdfName;
  const progress = searchParams?.get("progress");

  const courseId = searchParams?.get("course_id") as string;
  const [breadcrumbItems, setBreadcrumbItems] = useState<InnerItem[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false); // Add error state
  const { toast } = useToast() as unknown as ToastType;
  const instance_id = parsedData.id;
  const topicName = parsedData.topic_name;
  const { updateDocumentProgress } = useCourse();
  const [openFeedback, setOpenFeedback] = useState(false);
  const totalSlides = Number(searchParams?.get("page_count")) ?? 0;
  const [commentsData, setCommentsData] = useState<CommentsResponse[]>([]);
  const [commentCount, setCommentCount] = useState<number>(0);
  const sectionId = searchParams?.get("section_id") as string;
  const topicWise = searchParams?.get("is_topic_wise");
  const [isCommentsLoding, setIsCommentsLoading] = useState<boolean>(true);
  const [showComments, setShowComments] = useState(false);
  const [isLiked, setIsLiked] = useState(false);
  const { insertLogDetails } = UseLogClass();
  const { getComments, addComments } = useComments();
  const [likeCount, setLikeCount] = useState(0);
  const [LikeData, setLikeData] = useState<CommentsResponse[]>([]);
  const [userId, setUserId] = useState<string>("");

  useEffect(() => {
    const USER_DATA = localStorage.getItem(KEYS.USER_DETAILS);
    if (USER_DATA !== null) {
      const userInfo = JSON.parse(USER_DATA);
      setUserId(userInfo.id);
    }
    if (topicWise === "true") {
      setBreadcrumbItems(
        getBreadCrumbItems("Pdf Viewer Topicwise", {
          course_id: courseId,
          section_id: sectionId as string,
        })
      );
    } else {
      setBreadcrumbItems(
        getBreadCrumbItems("Pdf Viewer", {
          course_id: courseId as string,
        })
      );
    }
    getLikeData();
    getCommentData();
    checkUserLiked();
    insertLogDetails(
      "Course_Resource",
      "Resource Viewer",
      `${fileName} PDF Viewed `,
      "SUCCESS",
      instance_id
    );
  }, []);

  useEffect(() => {
    if (fileUrl) {
      setIsLoading(false);
    }
  }, [fileUrl]);

  const getLikeData = () => {
    const fetchLikeData = async (): Promise<void> => {
      const USER_DATA = localStorage.getItem(KEYS.USER_DETAILS);
      let currentUserId = "";
      if (USER_DATA !== null) {
        const userInfo = JSON.parse(USER_DATA);
        currentUserId = userInfo.id;
      }

      const org_id = localStorage.getItem(KEYS.ORG_ID);
      const params = {
        instance_id: instance_id as string,
        org_id: org_id as string,
        activity_type: "like",
      };
      try {
        const result = await getComments(params);

        setIsCommentsLoading(false);
        if (result.length > 0) {
          setLikeCount(result.length);
          setLikeData(result);
          const userHasLiked = result.some(
            (like) =>
              like.user_id === currentUserId && like.activity_type === "like"
          );
          setIsLiked(userHasLiked);
        }
      } catch (error) {
        console.log(error);
      }
    };
    fetchLikeData()
      .then((response) => {
        console.log(response);
      })
      .catch((error) => {
        console.log(error);
      });
  };

  const checkUserLiked = () => {
    const liked = LikeData.some(
      (comment) =>
        comment.user_id === userId && comment.activity_type === "like"
    );
    setIsLiked(liked);
  };

  const updateProgress = async (curresnt_slide: number) => {
    const orgID = getLocalStorageItem(KEYS.ORG_ID);
    const userID = getLocalStorageItem(KEYS.USER_ID);
    let params: UpdateDocumentRequest = {
      course_id: courseId as string,
      instance_id: instance_id,
      org_id: orgID as string,
      progress_data: { current_page: curresnt_slide },
      user_id: userID as string,
    };
    try {
      const result = await updateDocumentProgress(params);
      if (result) {
        toast({
          variant: "default",
          title: SUCCESS_MESSAGES.success,
          description: SUCCESS_MESSAGES.progress_msg,
        });
        await insertLogDetails(
          "Course_Resource",
          "Resource Viewer",
          `${fileName} PDF completed `,
          "SUCCESS",
          instance_id
        );
      }

      //  handleCancel() }
    } catch (error) {
      const err = error as ErrorCatch;
      await insertLogDetails(
        "Course_Resource",
        "Resource Viewer",
        `${fileName} PDF failed to complete `,
        "ERROR",
        instance_id
      );
      // toast({
      //   variant: "destructive",
      //   title: ERROR_MESSAGES.error,
      //   description: err?.message,
      // });
    }
  };
  const closeFeedback = () => {
    setOpenFeedback(false);
    getCommentData();
  };

  const getCommentData = () => {
    const fetchCommentData = async (): Promise<void> => {
      const org_id = localStorage.getItem(KEYS.ORG_ID);
      const params = {
        instance_id: instance_id as string,
        org_id: org_id as string,
        activity_type: "comment",
      };
      try {
        const result = await getComments(params);

        setIsCommentsLoading(false);
        if (result) {
          setCommentCount(result.length);
          setCommentsData(result);
        }
      } catch (error) {
        console.log(error);
      }
    };
    fetchCommentData()
      .then((response) => {
        console.log(response);
      })
      .catch((error) => {
        console.log(error);
      });
  };

  const handleCancel = () => {
    if (process.env.NEXT_PUBLIC_SUBJECT_WISE_LIST === "true") {
      router.push(`/pages/section-details`);
    } else {
      router.push(`/pages/course-details?course_id=${courseId}`);
    }
  };

  const handleOnError = () => {
    setHasError(true); // Set error state
    // Optionally, you could try to reload the PDF here:
    // window.location.reload(); // Less intrusive than full page reload
  };
  const addFeedback = () => {
    setOpenFeedback(true);
  };

  const handleLike = async () => {
    const reqParams = {
      comment_data: {
        subject: "",
        message: "",
        type: "Feedback",
        parent_id: null,
        activity_type: "like",
      },
      instance_id: instance_id,
      user_id: userId,
    };

    try {
      const result = await addComments(reqParams);

      if (result.status === "success") {
        toast({
          variant: "success",
          title: isLiked ? "Unliked!" : "Liked!",
          description: isLiked
            ? "You removed your like."
            : "You liked this material.",
        });
        setIsLiked(!isLiked);
        setLikeCount((prevCount) => (isLiked ? prevCount - 1 : prevCount + 1));
        await insertLogDetails(
          "Course_Resource",
          "Resource Viewer",
          `${fileName} Pdf Liked  `,
          "SUCCESS",
          result.comment_id
        );
      }
    } catch (error) {
      toast({
        variant: "destructive",
        title: "Error",
        description: "Action failed.",
      });
      await insertLogDetails(
        "Course_Resource",
        "Resource Viewer",
        `Failed to like ${fileName} pdf `,
        "ERROR",
        UUID
      );
    }
  };

  const handleComments = (): void => {
    getCommentData();
    setShowComments(!showComments);
  };

  return (
    <MainLayout titleText="">
      <NextBreadcrumb
        items={breadcrumbItems}
        separator={<span> | </span>}
        containerClasses="flex py-5"
        listClasses="hover:underline mx-2 font-bold"
        capitalizeLinks
      />
      <Card className="w-full sm:w-4/5 lg:w-1/2 max-w-full mx-auto p-4 sm:p-6 h-[150vh] flex flex-col text-[var(--color-font-color)]">
        <div className="flex-grow flex flex-col overflow-hidden">
          <div className="flex items-center gap-2 ">
            <p className="text-lg font-semibold mt-2 mb-2">
              {t("Topic")} : {topicName}
            </p>
          </div>
          <p className="text-lg font-semibold mt-2 mb-2">{fileName}</p>
          <div className="bg-gray-50 rounded-lg flex-grow overflow-hidden relative">
            {isLoading ? (
              <div className="absolute inset-0 flex items-center justify-center">
                <Spinner />
              </div> // Use your Spinner component
            ) : fileUrl ? (
              <DocumentViewer
                queryParams="hl=Nl"
                url={fileUrl}
                viewerUrl={
                  "https://docs.google.com/gview?url=%URL%&embedded=true"
                }
                viewer="url"
                key={fileUrl}
              />
            ) : hasError ? ( // Show message if there was an error
              <div className="absolute inset-0 flex items-center justify-center text-red-500">
                {t("Failed to load PDF. Please try again.")}
              </div>
            ) : null}
          </div>
          <p className="text-lg mt-2">
            {" "}
            {t("Total Pages")}: {parsedData.page_count}
          </p>
          <p className="text-lg mt-2">
            {t("Description")}: {parsedData.description}
          </p>
        </div>
        <div className="pt-5">
          {progress != "100" && (
            <div className="flex justify-end">
              <button
                onClick={() => updateProgress(totalSlides)}
                className="flex items-center px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700"
              >
                <CheckCircle className="mr-2" size={20} />{" "}
                {t("Mark as Completed")}
              </button>
            </div>
          )}
        </div>
        <div className="flex justify-between items-center mt-4 mb-4">
          <div className="flex items-center gap-2 ">
            <button
              onClick={handleLike}
              className="flex items-center gap-2 text-black-600 bg-gray-100 p-2 rounded transition"
              title={isLiked ? "Unlike" : "Like"}
            >
              <ThumbsUp
                className={`w-5 h-5 ${
                  isLiked ? "text-blue-600 fill-blue-600" : "text-gray-600"
                }`}
                fill={isLiked ? "currentColor" : "none"}
              />
              {likeCount > 0 && (
                <span className="text-sm font-medium text-[var(--color-font-color)]">
                  {likeCount} {t(likeCount === 1 ? "Like" : "Likes")}
                </span>
              )}
            </button>
          </div>

          <button
            onClick={handleComments}
            className="flex items-center gap-2 text-black-600 bg-gray-100 p-2 rounded transition"
          >
            <MessageCircle className="w-5 h-5 text-orange-600" />
            <span className="text-sm font-medium text-[var(--color-font-color)]">
              {t("Comments")}
            </span>
          </button>
        </div>
        {showComments && (
          <div>
            <div className="flex justify-between items-center gap-2 mb-2 mt-5">
              <div className="flex items-center gap-2">
                <MessageCircle className="w-5 h-5 text-orange-600" />
                <h2 className="text-xl font-semibold"> {t("Comments")} </h2>
              </div>
              <div className="cursor-pointer" title="Add comments">
                <MessageSquarePlus
                  color="orange"
                  size={24}
                  onClick={addFeedback}
                />
              </div>
            </div>

            <div>
              {!isCommentsLoding ? (
                <div className="space-y-4 p-4 bg-gray-50 rounded-lg">
                  <CommentsSection
                    id={instance_id}
                    commentsData={commentsData}
                    onCommentAdded={getCommentData}
                  ></CommentsSection>
                </div>
              ) : (
                <Spinner></Spinner>
              )}
            </div>
          </div>
        )}
      </Card>

      <div className="flex justify-end mt-4 sticky bottom-2 px-2 ">
        <Button variant="outline" className="rounded-md" onClick={handleCancel}>
          {t("Back")}
        </Button>
        {openFeedback && (
          <Modal
            title="Add Feedback/Suggestion"
            header=""
            openDialog={openFeedback}
            closeDialog={() => {
              closeFeedback();
            }}
            type="max-w-xl"
          >
            <CommentsModal
              closeDialog={() => {
                closeFeedback();
              }}
              instanceId={instance_id}
            />
          </Modal>
        )}
      </div>
    </MainLayout>
  );
}
