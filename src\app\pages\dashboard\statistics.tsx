// "use client";

// import React, { useEffect, useState } from "react";
// import { Trophy, Target, Clock, Award } from "lucide-react";
// import { getLocalStorageItem } from "@/lib/utils";
// import { useCourse } from "@/hooks/useCourse";
// import { KEYS } from "@/lib/keys";
// import { CourseStats, UserStatistics } from "@/types";
// import { Spinner } from "@/components/ui/progressiveLoder";
// import AchievementCard from "@/components/dashboard/acheivementCard";
// import DBUserCourseViews from "@/components/dashboard/courseViews";
// import { Modal } from "@/components/ui/modal";
// import CourseDetailsModal from "./courseDetailsModal";
// import { ACHIEVEMENTS, ACQUIRED_MARKS, COVERED_PERCENTAGE, COVERED_TIME } from "@/lib/constants";

// interface DashboardStatus {
//   totalMarksGot: number;
//   percCompleted: number;
//   hoursCompleted: string;
//   achievements: number;
// }

// const DashboardStatistics = () => {
//   const { getUserCourseStatics } = useCourse();
//   const orgID = getLocalStorageItem(KEYS.ORG_ID);
//   const [isLoading, setIsLoading] = useState(false);
//   const [courseStatisticsAll, setCourseStaticsAll] = React.useState<UserStatistics[]>([]);
//   const [userStatistics, setUserStatics] = React.useState<UserStatistics[]>([]);
//   const [dashboardStats, setDashboardStats] = React.useState<DashboardStatus>();
//   const [courseListWithCounter, setCourseListWithCounter] = React.useState<CourseStats[]>([]);
//   const [openModal, setIsOpenModal] = React.useState<boolean>(false);
//   const [courseLength, setCourseLength] = React.useState<number>(0)

//   useEffect(() => {
//     getStatisticsDetails();
//   }, []);

//   const convertTimeStringToHours = (time: string): number => {
//     // Check if the input is a string that can be converted to a number
//     const timeInSeconds = Number(time);
//     if (!isNaN(timeInSeconds)) {
//       // If it's in seconds, convert seconds to hours by dividing by 3600
//       return parseFloat((timeInSeconds / 3600).toFixed(2)); // Convert seconds to hours
//     }

//     // If it's not just a number, check for hh:mm:ss format
//     if (typeof time !== "string" || !time.includes(":")) {
//       console.error(
//         "Invalid input: expected a string in 'hh:mm:ss' format or seconds"
//       );
//       return 0;
//     }

//     const timeParts = time.split(":");

//     // Ensure we have exactly 3 parts (hours, minutes, seconds)
//     if (timeParts.length !== 3) {
//       console.error("Invalid time format: expected 'hh:mm:ss'");
//       return 0;
//     }

//     const [hoursStr, minutesStr, secondsStr] = timeParts;

//     // Parse the time components
//     const hours = Number(hoursStr);
//     const minutes = Number(minutesStr);
//     const seconds = Number(secondsStr);

//     // Check if all values are valid numbers
//     if (isNaN(hours) || isNaN(minutes) || isNaN(seconds)) {
//       console.error("Invalid time components: unable to convert to numbers");
//       return 0;
//     }

//     // Calculate total hours
//     return parseFloat((hours + minutes / 60 + seconds / 3600).toFixed(2));
//   };

//   // Function to convert HH:MM:SS to seconds
//   const timeToSeconds = (time: string) => {
//     const [hours, minutes, seconds] = time.split(":").map(Number);
//     return hours * 3600 + minutes * 60 + seconds;
//   };

//   const getStatisticsDetails = async (): Promise<void> => {
//     try {
//       let user_id = getLocalStorageItem(KEYS.USER_ID) || "";
//       const response = await getUserCourseStatics({
//         org_id: orgID as string,
//         course_id: null,
//         user_id: user_id as string,
//       });

//       setCourseStaticsAll(response);
//       setIsLoading(true);
      
//       setCourseLength(response?.length)
//       const firstFiveItems = response.slice(0, 5);
//       const allItems = response;

//       setUserStatics(firstFiveItems);

//       // calculate cumulative percentage of time
//       const totalProgress = allItems.reduce((sum, progress) => sum + progress.progress, 0);
//       const cumulativePercentage = (totalProgress / allItems.length);

//       // Calculate the total time spent
//       const totalTimeInSeconds = allItems.reduce((sum, progress) => sum + timeToSeconds(String(progress.time_spent)), 0); //firstFiveItems.map(timeToSeconds).reduce((sum, seconds) => sum + seconds, 0);

//       // Convert back to HH:MM:SS format
//       const totalHoursCovered = totalTimeInSeconds;

//       const totalMarks =
//       allItems.reduce(
//           (accumulator, currentValue) =>
//             accumulator + Number(currentValue.totalMarks),
//           0
//         ) || 0;
//       const totalPercentCovered =
//       allItems.reduce(
//           (accumulator, currentValue) =>
//             cumulativePercentage,
//           0
//         ) || 0;

//       const totalAchievements =
//       allItems.reduce(
//           (accumulator, currentValue) =>
//             accumulator + Number(currentValue.achievements),
//           0
//         ) || 0;
//       const overAllStatus = {
//         totalMarksGot: totalMarks,
//         percCompleted: totalPercentCovered,
//         hoursCompleted: String(totalHoursCovered),
//         achievements: totalAchievements,
//       };

//       setDashboardStats(overAllStatus as DashboardStatus);
//     } catch (error) {
//       setIsLoading(true);
//     }
//   };

//   const showCardContents = (detailsType: string) => {
//     switch (detailsType) {
//       case ACQUIRED_MARKS:
//         const statsData = courseStatisticsAll.map((stats) => ({
//           course_name: stats.course_name,
//           valuetype: "Acquired Marks",
//           value: stats.totalMarks
//         }));
//         setCourseListWithCounter(statsData as CourseStats[]);
//         break;
//       case COVERED_PERCENTAGE:
//         const coveredPerncData = courseStatisticsAll.map((stats) => ({
//           course_name: stats.course_name,
//           valuetype: "Progress",
//           value: stats.progress
//         }));
//         setCourseListWithCounter(coveredPerncData as CourseStats[]);
//         break;
//       case COVERED_TIME:
//         const coveredTimeData = courseStatisticsAll.map((stats) => ({
//           course_name: stats.course_name,
//           valuetype: "Time Spent",
//           value: stats.time_spent
//         }));
//         setCourseListWithCounter(coveredTimeData as CourseStats[]);
//         break;
//       case ACHIEVEMENTS:
//         const achievementsData = courseStatisticsAll.map((stats) => ({
//           course_name: stats.course_name,
//           valuetype: "Achievements",
//           value: stats.achievements
//         }));
//         setCourseListWithCounter(achievementsData as CourseStats[]);
//         break;
//     }

//     setIsOpenModal(true);
//   };
//   return (
//     <div>
//       {isLoading ? (
//         <main className="flex-1 ">
//           <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4 ">
//             <AchievementCard
//               Icon={Trophy}
//               Title="Total Score"
//               Value={(dashboardStats?.totalMarksGot as number) ?? 0}
//               Subtitle="marks"
//               ColorClass="bg-blue-500"
//               // onClick={() => showCardContents(ACQUIRED_MARKS)}
//             />
//             <AchievementCard
//               Icon={Target}
//               Title="Completed"
//               Value={
//                 dashboardStats?.percCompleted
//                   ? parseFloat(dashboardStats?.percCompleted.toFixed(2))
//                   : 0
//               }
//               Subtitle="%"
//               ColorClass="bg-green-500"
//               onClick={() => showCardContents(COVERED_PERCENTAGE)}
//             />
//             <AchievementCard
//               Icon={Clock}
//               Title="Study Hours"
//               Value={convertTimeStringToHours(
//                 dashboardStats?.hoursCompleted as string
//               )}
//               Subtitle="hrs"
//               ColorClass="bg-purple-500"
//               onClick={() => showCardContents(COVERED_TIME)}
//             />
//             <AchievementCard
//               Icon={Award}
//               Title="Achievements"
//               Value={dashboardStats?.achievements ?? 0}
//               Subtitle="earned"
//               ColorClass="bg-yellow-500"
//               onClick={() => showCardContents(ACHIEVEMENTS)}
//             />
//           </div>
//         </main>
//       ) : (
//         <Spinner></Spinner>
//       )}

//       {userStatistics.length > 0 && (
//         <DBUserCourseViews courseStats={userStatistics}  courseLength = {courseLength} />
//       )}

//       {openModal && (
//         <Modal
//           title="Course wise Statistics"
//           header=""
//           openDialog={openModal}
//           closeDialog={() => {
//             setIsOpenModal(false);
//           }}
//           type="max-w-5xl"
//         >
//           <CourseDetailsModal
//             closeDialog={() => {
//               setIsOpenModal(false);
//             } } CourseStatistics={courseListWithCounter}          />
//         </Modal>
//       )}
//     </div>
//   );
// };

// export default DashboardStatistics;
