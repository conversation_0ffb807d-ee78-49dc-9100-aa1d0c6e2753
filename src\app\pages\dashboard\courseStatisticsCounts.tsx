"use client";

import React, { useEffect, useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON>, CheckCircle, Clock, Maximize2 } from "lucide-react";
import { getLocalStorageItem } from "@/lib/utils";
import { useCourse } from "@/hooks/useCourse";
import { KEYS } from "@/lib/keys";
import { CourseStats, SummaryStatistics, UserStatistics } from "@/types";
import { Spinner } from "@/components/ui/progressiveLoder";
import { Modal } from "@/components/ui/modal";
import CourseDetailsModal from "./courseDetailsModal";
import {
  ACHIEVEMENTS,
  ACQUIRED_MARKS,
  COVERED_PERCENTAGE,
  COVERED_TIME,
} from "@/lib/constants";
import { Card, CardContent } from "@/components/ui/card";
import "../../../styles/dashboard.css";
import {
  convertDecimalHoursToHrMin,
  convertTimeStringToHours,
  timeToSeconds,
} from "@/lib/timeUtils";
import Image from "next/image";

interface CourseStatistics {
  totalMarksGot: number;
  percCompleted: number;
  hoursCompleted: string;
  achievements: number;
}

interface CourseStatisticsProps {
  courseId: string;
}

const CourseStatisticsCounts = ({ courseId }: CourseStatisticsProps) => {
  const { getUserCourseStatics, getUserAllCourseStatics } = useCourse();
  const orgID = getLocalStorageItem(KEYS.ORG_ID);
  const [isLoading, setIsLoading] = useState(false);
  const [courseStatisticsAll, setCourseStaticsAll] = React.useState<
    UserStatistics[]
  >([]);
  const [userStatistics, setUserStatics] = React.useState<UserStatistics[]>([]);
  const [dashboardStats, setDashboardStats] =
    React.useState<CourseStatistics>();
  const [courseListWithCounter, setCourseListWithCounter] = React.useState<
    CourseStats[]
  >([]);
  const [openModal, setIsOpenModal] = React.useState<boolean>(false);
  const [courseLength, setCourseLength] = React.useState<number>(0);
  const [summaryData, setSummaryData] = useState<SummaryStatistics[]>([]);

  useEffect(() => {
   
    getCourseStatisticsDetails(courseId);
  }, []);

  const getCourseStatisticsDetails = async (
    enrolledCourseId: string | null
  ): Promise<void> => {
    try {
      let user_id = getLocalStorageItem(KEYS.USER_ID) || "";

      let apiParams = {
        org_id: orgID as string,
        course_id: enrolledCourseId,
        user_id: user_id as string,
      };

      const response = await getUserCourseStatics(apiParams);
      console.log("response21", response);
      let filteredData1 = response.filter(
        (item) =>
          // item.quiz_type === "checkpoint" ||
          item.quiz_type === "Main" ||
          item.resource_type === "url" ||
          item.resource_type === "file" ||
          item.resource_type === "page"
          
      );
      // let filteredData2 = response.filter(
      //   (item) =>
      //     item.resource_type === "page" ||
      //     item.resource_type === "url" ||
      //     item.resource_type === "file"
          
      // );
      console.log("filteredData1", filteredData1);
      setCourseStaticsAll(filteredData1);
      setIsLoading(true);
      setCourseLength(filteredData1?.length);
      const firstFiveItems = filteredData1.slice(0, 5);
      const allItems1 = filteredData1;
      // const allItems2 = filteredData2;
      setUserStatics(firstFiveItems);

      // calculate cumulative percentage of time
      const totalProgress = allItems1.reduce(
        (sum, progress) => sum + progress.progress,
        0
      );
      const cumulativePercentage = totalProgress / allItems1.length;

      // Calculate the total time spent
      const totalTimeInSeconds = allItems1.reduce((sum, progress) => {
        if (progress.time_spent !== null) {
          return sum + timeToSeconds(String(progress.time_spent));
        }
        return sum;
      }, 0);

      // Convert back to HH:MM:SS format
      console.log("totalTimeInSeconds", totalTimeInSeconds);
      const totalHoursCovered = totalTimeInSeconds;

      const totalMarks =
        allItems1.reduce(
          (accumulator, currentValue) =>
            accumulator + Number(currentValue.totalMarks),
          0
        ) || 0;
      const totalPercentCovered =
       allItems1.reduce(
          (accumulator, currentValue) => cumulativePercentage,
          0
        ) || 0;

      const totalAchievements =
        allItems1.reduce(
          (accumulator, currentValue) =>
            accumulator + Number(currentValue.achievements),
          0
        ) || 0;

      const overAllStatus = {
        totalMarksGot: totalMarks,
        percCompleted: totalPercentCovered,
        hoursCompleted: String(totalHoursCovered),
        achievements: totalAchievements,
      };

      setDashboardStats(overAllStatus as CourseStatistics);

      setSummaryData([
        {
          title: ACQUIRED_MARKS,
          value: overAllStatus.totalMarksGot,
          trend: "",
          gradient: "bg-total-marks",
          // "bg-gradient-to-r from-pink-500 via-pink-600 to-pink-700",
          icon: (
            <Image
              src="/assets/total-marks.png"
              alt="Profile Image"
              width={50}
              height={50}
              className=""
            />
          ),
          textColor: "text-white",
        },
        {
          title: COVERED_PERCENTAGE,
          value: `${overAllStatus.percCompleted.toFixed(2)}%`,
          trend: "",
          gradient: "bg-progress",
          // "from-purple-500 via-purple-600 to-purple-800",
          icon: (
            <Image
              src="/assets/progress.png"
              alt="Profile Image"
              width={50}
              height={50}
              className=""
            />
          ),
          textColor: "text-white",
        },
        {
          title: COVERED_TIME,
          value: `${convertTimeStringToHours(String(totalHoursCovered))}`,
          trend: "",
          gradient: "bg-time-spent",
          // "from-cyan-400 via-blue-500 to-indigo-600",
          icon: (
            <Image
              src="/assets/time-spent.png"
              alt="Profile Image"
              width={50}
              height={50}
              className=""
            />
          ),
          textColor: "text-white",
        },
        {
          title: ACHIEVEMENTS,
          value: `${overAllStatus.achievements}`,
          trend: "",
          gradient: "bg-achievements",
          // "from-amber-400 via-amber-500 to-amber-600",
          icon: (
            <Image
              src="/assets/acheivment.png"
              alt="Profile Image"
              width={50}
              height={50}
              className=""
            />
          ),
          textColor: "text-white",
        },
      ]);
    } catch (error) {
      setIsLoading(true);
    }
  };

  const showCardContents = (detailsType: string) => {
  
    switch (detailsType) {
      case ACQUIRED_MARKS:
        const statsData = courseStatisticsAll.map((stats) => ({
          course_name:
            courseId == null ? stats.course_name : stats.resource_name,
          valuetype: "Acquired Marks",
          value: stats.totalMarks,
          section_name: stats.section_name,
        }));
        setCourseListWithCounter(statsData as CourseStats[]);
        break;
      case COVERED_PERCENTAGE:
        const coveredPerncData = courseStatisticsAll.map((stats) => ({
          course_name:
            courseId == null ? stats.course_name : stats.resource_name,
          valuetype: "Progress",
          value: stats.progress,
          section_name: stats.section_name,
        }));
      
        setCourseListWithCounter(coveredPerncData as CourseStats[]);
        break;
      case COVERED_TIME:
        const coveredTimeData = courseStatisticsAll.map((stats) => ({
          course_name:
            courseId == null ? stats.course_name : stats.resource_name,
          valuetype: "Time Spent",
          value: stats.time_spent,
          section_name: stats.section_name,
        }));

        setCourseListWithCounter(coveredTimeData as CourseStats[]);
        break;
      case ACHIEVEMENTS:
        const achievementsData = courseStatisticsAll.map((stats) => ({
          course_name:
            courseId == null ? stats.course_name : stats.resource_name,
          valuetype: "Achievements",
          value: stats.achievements,
          section_name: stats.section_name,
        }));
        setCourseListWithCounter(achievementsData as CourseStats[]);
        break;
    }

    setIsOpenModal(true);
  };
  return (
    <div>
      {isLoading ? (
        <main className="w-full">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
            {summaryData.map((item, index) => (
              <Card
                key={index}
                onClick={() => showCardContents(item.title)}
                className={`transition-all transform rounded duration-500 w-100 cursor-pointer
                         
                          ${item.gradient}
                      backdrop-blur-xl bg-opacity-5
                         border border-white/20 shadow-xl
                         `}
              >
                <CardContent>
                  <div
                    className={`mt-2 text-base ${
                      item.textColor || "text-black"
                    }`}
                  >
                    {item.title}
                  </div>
                  <div
                    className={`text-2xl font-bold ${
                      item.textColor || "text-white"
                    } flex justify-center`}
                  >
                    {item.value}
                  </div>

                  <div
                    className={`bottom-5 left-3 ${
                      item.textColor || "text-white"
                    }  flex items-center`}
                  >
                    {item.icon}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </main>
      ) : (
        <Spinner></Spinner>
      )}

      {openModal && (
        <Modal
          title=""
          header=""
          openDialog={openModal}
          closeDialog={() => {
            setIsOpenModal(false);
          }}
          type="max-w-3xl"
        >
          <CourseDetailsModal
            closeDialog={() => {
              setIsOpenModal(false);
            }}
            CourseStatistics={courseListWithCounter}
            CourseId={courseId}
            isMain={false}
          />
        </Modal>
      )}
    </div>
  );
};

export default CourseStatisticsCounts;
