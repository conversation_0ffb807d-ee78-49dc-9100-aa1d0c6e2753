import { CustomBrandingDetails } from '@/types';
import { useEffect } from 'react';

export const useThemeColors = (theme: string) => {
  useEffect(() => {
    const fallbackThemes: Record<string, Partial<CustomBrandingDetails>> = {
      light: {
        app_background_color: "#f5f5f5",
        navigation_text_color: "#27548A",
        footer_background_color: "#ffffff",
        footer_text_color: "#000000",
        button_primary_color: "#9fc089",
        button_primary_text_color: "#ffffff",
        button_secondary_color: "#000000",
        button_secondary_text_color: "#ffffff",
        button_dismiss_bg_color: "#000000",
        button_dismiss_text_color: "#ffffff",
        button_info_background_color: "#d1ecf1",
        button_info_text_color: "#0c5460",
        toast_success_color: "#28a745",
        toast_error_color: "#dc3545",
        toast_warning_color: "#ffc107",
        toast_info_color: "#17a2b8",
        font_family: "Arial, sans-serif",
        top_bar_background_color: "#000000",
        navbar_text_color: "#27548A",
        navbar_text_color_hover: "#27548A",
        font_base_size: "16px",
        navbar_background_color: "#ffffff",
        font_color: "#000000",
      },
      dark: {
        app_background_color: "#000000",
        navigation_text_color: "#ffffff",
        footer_background_color: "#000000",
        footer_text_color: "#dddddd",
        button_primary_color: "#9fc089",
        button_primary_text_color: "#ffffff",
        button_secondary_color: "#000000",
        button_secondary_text_color: "#ffffff",
        button_dismiss_bg_color: "#000000",
        button_dismiss_text_color: "#ffffff",
        button_info_background_color: "#00bcd4",
        button_info_text_color: "#ffffff",
        toast_success_color: "#66bb6a",
        toast_error_color: "#ef5350",
        toast_warning_color: "#ffa726",
        toast_info_color: "#29b6f6",
        font_family: "Roboto, sans-serif",
         top_bar_background_color: "#000000",
        navbar_text_color: "#27548A",
        navbar_text_color_hover: "#27548A",
        font_base_size: "16px",
        navbar_background_color: "#ffffff",
        font_color: "#000000",
      },
      custom: {
        app_background_color: "#f5f5f5",
        navigation_text_color: "#27548A",
        footer_background_color: "#ffffff",
        footer_text_color: "#000000",
        button_primary_color: "#9fc089",
        button_primary_text_color: "#ffffff",
        button_secondary_color: "#000000",
        button_secondary_text_color: "#ffffff",
        button_dismiss_bg_color: "#000000",
        button_dismiss_text_color: "#ffffff",
        button_info_background_color: "#d1ecf1",
        button_info_text_color: "#0c5460",
        toast_success_color: "#28a745",
        toast_error_color: "#dc3545",
        toast_warning_color: "#ffc107",
        toast_info_color: "#17a2b8",
        font_family: "Arial, sans-serif",
        top_bar_background_color: "#000000",
        navbar_text_color: "#27548A",
        navbar_text_color_hover: "#27548A",
        font_base_size: "16px",
        navbar_background_color: "#ffffff",
        font_color: "#000000",
      },
    };

    const raw = localStorage.getItem('brandingDetails');
    let brand: Partial<CustomBrandingDetails> = fallbackThemes[theme] || {};

    try {
      if (raw) {
        const allBrands: CustomBrandingDetails[] = JSON.parse(raw);
        const matched = allBrands.find(b => b.theme_name === theme);
        if (matched) {
          brand = { ...fallbackThemes[theme], ...matched };
        }
      }

      const colorMap: Record<string, string | undefined> = {
        '--color-primary': brand.top_bar_background_color,
        '--color-background': brand.app_background_color,
        '--color-nav-text': brand.navbar_text_color,
        '--color-nav-hover-text': brand.navbar_text_color_hover,
        '--color-footer-bg': brand.footer_background_color,
        '--color-footer-text': brand.footer_text_color,
        '--color-button-primary': brand.button_primary_color,
        '--color-button-primary-text': brand.button_primary_text_color,
        '--color-button-secondary': brand.button_secondary_color,
        '--color-button-secondary-text': brand.button_secondary_text_color,
        '--color-button-dismiss': brand.button_dismiss_bg_color,
        '--color-button-dismiss-text': brand.button_dismiss_text_color,
        '--color-button-info': brand.button_info_background_color,
        '--color-button-info-text': brand.button_info_text_color,
        '--color-toast-success': brand.toast_success_color,
        '--color-toast-error': brand.toast_error_color,
        '--color-toast-warning': brand.toast_warning_color,
        '--color-toast-info': brand.toast_info_color,
        '--font-family': brand.font_family,
        '--font-size': brand.font_base_size,
        '--color-nav-bg':  brand.navbar_background_color,
        '--color-font-color': brand.font_color

      };

      const root = document.documentElement;
      Object.entries(colorMap).forEach(([varName, colorValue]) => {
        root.style.setProperty(varName, colorValue ?? '');
      });
    } catch (err) {
      console.error('Failed to load branding details:', err);
    }
  }, [theme]);
};
