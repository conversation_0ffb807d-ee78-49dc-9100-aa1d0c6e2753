"use client";

import React, { useState, useEffect } from "react";
import MainLayout from "../layouts/mainLayout";
import { useTranslation } from "next-i18next";
import {
  Check,
  ChevronDown,
  ChevronRight,
  Search,
  FolderOpen,
  CornerDownRight,
  Folder,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { UseTopics } from "@/hooks/useTopics";
import { ErrorCatch, ToastType, TopicsData, CourseData } from "@/types";
import { useToast } from "@/components/ui/use-toast";
import { useRouter } from "next/navigation";
import { KEYS } from "@/lib/keys";
import { getLocalStorageItem } from "@/lib/utils";
import { ERROR_MESSAGES } from "@/lib/messages";

export default function Topics(): React.JSX.Element {
  const { t } = useTranslation("common");
  const { getTopicsData } = UseTopics();
  const { toast } = useToast() as ToastType;
  const router = useRouter();
  const [expandedTopics, setExpandedTopics] = useState<{
    [key: number]: boolean;
  }>({});
  const [expandedSubtopics, setExpandedSubtopics] = useState<{
    [key: string]: boolean;
  }>({});
  const [selectedCourses, setSelectedCourses] = useState<string[]>([]);
  const [isCourseSelected, setIsCourseSelected] = useState<boolean>(false);
  const [selectedCourseData, setSelectedCourseData] = useState<CourseData>();
  const [topicHierarchy, setTopicHierarchy] = useState<TopicsData[]>([]);
  const [searchTerm, setSearchTerm] = useState<string>("");
  const orgID = getLocalStorageItem(KEYS.ORG_ID);

  useEffect(() => {
    fetchTopics();
  }, []);

  const fetchTopics = async (): Promise<void> => {
    const params = {
      org_id: orgID ?? "",
      filter_data: 0,
    };
    try {
      const response: TopicsData[] = await getTopicsData(params);
      setTopicHierarchy(response);
    } catch (error) {
      const err = error as ErrorCatch;
      toast({
        variant: "destructive",
        title: ERROR_MESSAGES.error,
        description: err?.message,
      });
    }
  };

  const toggleTopic = (index: number) => {
    setExpandedTopics((prev) => ({ ...prev, [index]: !prev[index] }));
  };

  const toggleSubtopic = (topicIndex: number, subtopicIndex: number) => {
    setExpandedSubtopics((prev) => ({
      ...prev,
      [`${topicIndex}-${subtopicIndex}`]:
        !prev[`${topicIndex}-${subtopicIndex}`],
    }));
  };

  const handleCourseSelection = (
    index: number,
    subIndex: number | null,
    courseIndex: number,
    courseData: CourseData,
    topic: TopicsData
  ) => {
    const selectedCourse =
      subIndex === null
        ? `${index}-${courseIndex}`
        : `${index}-${subIndex}-${courseIndex}`;
    setSelectedCourses([selectedCourse]);
    setSelectedCourseData(courseData);
    setIsCourseSelected(true);
    localStorage.setItem(KEYS.SELECTED_TOPIC, topic.name as string);
  };

  const handleCourse = (): void => {
    localStorage.setItem(
      KEYS.SELECTED_COURSE,
      selectedCourseData?.full_name as string
    );
    const courseId = selectedCourseData?.course_id ?? "";
    localStorage.setItem(KEYS.COURSE_ID, courseId);
    router.push("/pages/dashboard");
  };

  const isSelected = (
    index: number,
    subIndex: number | null,
    courseIndex: number
  ) => {
    return subIndex === null
      ? selectedCourses.includes(`${index}-${courseIndex}`)
      : selectedCourses.includes(`${index}-${subIndex}-${courseIndex}`);
  };

  const filteredTopics = topicHierarchy.filter(
    (topic) =>
      topic.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (topic.courses &&
        topic.courses.some((course) =>
          course.course_name.toLowerCase().includes(searchTerm.toLowerCase())
        )) ||
      (topic.children &&
        topic.children.some(
          (subtopic) =>
            subtopic.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
            (subtopic.courses &&
              subtopic.courses.some((course) =>
                course.course_name
                  .toLowerCase()
                  .includes(searchTerm.toLowerCase())
              ))
        ))
  );

  return (
    <MainLayout titleText="" showMenuBar={false}>
      <div className="w-full">
        <div className="bg-[#F3F3F3] rounded-3xl flex items-center px-4 h-10">
          <Search className="text-[#00AFBB] mr-6" />
          <input
            type="text"
            placeholder={t("Search")}
            className="appearance-none border-none border-transparent outline-0 outline-transparent outline-none bg-transparent"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
        <div>
          {filteredTopics.map((topic, index) => (
            <div key={index} className="mb-4 mt-8 bg-[#FFF8F1] rounded-3xl">
              <div
                className={`flex items-center justify-between cursor-pointer text-white p-4 ${
                  expandedTopics[index]
                    ? "rounded-t-3xl bg-[#FDB666]"
                    : "rounded-3xl bg-[#FDB666]"
                } ${
                  !topic.children ||
                  topic.children.length === 0 ||
                  !topic.courses ||
                  topic.courses.length === 0
                    ? "rounded-3xl bg-[#FDB666]"
                    : ""
                }`}
                onClick={() => toggleTopic(index)}
              >
                <div className="flex items-center">
                  {expandedTopics[index] ? (
                    <FolderOpen className="mr-2" />
                  ) : (
                    <Folder className="mr-2" />
                  )}
                  <h2 className="font-bold">{topic.name}</h2>
                </div>
                {expandedTopics[index] ? <ChevronDown /> : <ChevronRight />}
              </div>
              {expandedTopics[index] && (
                <>
                  {topic.courses && topic.courses.length > 0 && (
                    <div className="ml-4 p-4 rounded-lg">
                      {topic.courses.map((course, courseIndex) => (
                        <div
                          key={courseIndex}
                          className="flex items-center mb-2 cursor-pointer"
                          onClick={() =>
                            handleCourseSelection(
                              index,
                              null,
                              courseIndex,
                              course,
                              topic
                            )
                          }
                        >
                          <CornerDownRight className="mr-2 text-[#9FC089]" />
                          <p className="text-[#9FC089] ps-2">
                            {course.course_name}
                          </p>
                          {isSelected(index, null, courseIndex) && (
                            <Check className="ml-auto h-5 w-5 text-[#9FC089]" />
                          )}
                        </div>
                      ))}
                    </div>
                  )}
                  {topic.children && (
                    <div className="ml-4 p-4 rounded-lg">
                      {topic.children.map((subtopic, subIndex) => (
                        <div key={subIndex} className="mb-2">
                          <div
                            className="flex items-center justify-between cursor-pointer"
                            onClick={() => toggleSubtopic(index, subIndex)}
                          >
                            <div className="flex items-center bg-[#F3F3F3] w-full rounded-3xl ms-4 me-4 ps-4 h-10">
                              {expandedSubtopics[`${index}-${subIndex}`] ? (
                                <FolderOpen className="mr-2 text-[#9FC089]" />
                              ) : (
                                <Folder className="mr-2 text-[#9FC089]" />
                              )}
                              <h3>{subtopic.name}</h3>
                            </div>
                          </div>
                          {expandedSubtopics[`${index}-${subIndex}`] &&
                            subtopic.courses && (
                              <div className="m-4 ps-4">
                                {subtopic.courses.map((course, courseIndex) => (
                                  <div
                                    key={courseIndex}
                                    className="flex items-center mb-2 cursor-pointer"
                                    onClick={() =>
                                      handleCourseSelection(
                                        index,
                                        subIndex,
                                        courseIndex,
                                        course,
                                        topic
                                      )
                                    }
                                  >
                                    <CornerDownRight className="mr-2 text-[#9FC089]" />
                                    <p className="text-[#9FC089] ps-2">
                                      {course.course_name}
                                    </p>
                                    {isSelected(
                                      index,
                                      subIndex,
                                      courseIndex
                                    ) && (
                                      <Check className="ml-auto h-5 w-5 text-[#9FC089]" />
                                    )}
                                  </div>
                                ))}
                              </div>
                            )}
                        </div>
                      ))}
                    </div>
                  )}
                </>
              )}
            </div>
          ))}
        </div>

        <div className="w-full flex justify-center sticky bottom-0 bg-[#FFF8F1] py-4">
          <Button
            className="text-white bg-[#9FC089] text-lg rounded-3xl w-full max-w-md hover:bg-[#9FC089]"
            disabled={!isCourseSelected}
            onClick={handleCourse}
          >
            {t("Continue")}
          </Button>
        </div>
      </div>
    </MainLayout>
  );
}
