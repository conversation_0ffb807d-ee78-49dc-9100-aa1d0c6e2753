import { FC } from 'react';

interface TableProps {
  data: { course: string; progress: number; grade: string }[];
}

const Table: FC<TableProps> = ({ data }) => {
  return (
    <div className="overflow-x-auto">
      <table className="min-w-full bg-white border border-gray-200">
        <thead>
          <tr>
            <th className="py-2 px-4 border-b">Course</th>
            <th className="py-2 px-4 border-b">Progress</th>
            <th className="py-2 px-4 border-b">Grade</th>
          </tr>
        </thead>
        <tbody>
          {data.map((row, idx) => (
            <tr key={idx} className="text-center">
              <td className="py-2 px-4 border-b">{row.course}</td>
              <td className="py-2 px-4 border-b">{row.progress}%</td>
              <td className="py-2 px-4 border-b">{row.grade}</td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};

export default Table;
