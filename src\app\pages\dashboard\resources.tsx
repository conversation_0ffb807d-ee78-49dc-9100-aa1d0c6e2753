import { useState, useEffect } from "react";
import { BookOpen } from "lucide-react";
import ViewResources from "./viewResource";
import { useCourse } from "@/hooks/useCourse";
import { getLocalStorageItem } from "@/lib/utils";
import { KEYS } from "@/lib/keys";
import { AllResourceResponse } from "@/types";
import React from "react";
import { useRouter } from "next/navigation";
import { Spinner } from "@/components/ui/progressiveLoder";
import { Modal } from "@/components/ui/modal";
import ViewAllResourceModal from "../section-details/viewAllResourceModal";
interface ResourcesLisProps {
  courseID: string;
  hideExam?: boolean;
  isModal?: boolean;

}
const ResourcesList: React.FC<ResourcesLisProps> = ({ courseID, hideExam ,isModal}) => {
  const [AllVideoResources, setAllVideoResources] = React.useState<
    AllResourceResponse[]
  >([]);
  const [filteredVideoResources, setFilteredVideoResources] = React.useState<
  AllResourceResponse[]
>([]);
  const [isLoading, setIsLoading] = useState(false);
  const { getAllResoures } = useCourse();
  const orgID = getLocalStorageItem(KEYS.ORG_ID);
  const router = useRouter();
  const configData = JSON.parse(localStorage.getItem("configurations") || "{}");
    const [viewAllResource, setViewAllResource] =
      React.useState(false);
    const [resourceDisplayCount, setResourceDisplayCount] = React.useState<number>(0);
     const [showViewAll, setShowViewAll] = React.useState(false);
  useEffect(() => {
    setResourceDisplayCount(configData?.assignments?.no_of_items || 0);
    getAllResourceDetails();
    localStorage.setItem('isCheckpointCanceled', 'false');
  }, []);

  const getAllResourceDetails = async (): Promise<void> => {
    try {
      let user_id = getLocalStorageItem(KEYS.USER_ID) || "";
      const response = await getAllResoures({
        org_id: orgID as string,
        course_id: courseID as string,
        user_id: user_id as string,
      });
      setIsLoading(true);
      const filteredVideoResult = {
        ...response.result,
        resources: response.result.filter(
          (resource) =>
            (resource.resource_type === "URL" ||
              resource.resource_type === "File" ||
              resource.resource_type === "Page") &&
            resource.resource_id
        ),
      };
      const configData = JSON.parse(
        localStorage.getItem("configurations") || "{}"
      );

      const displayCount = configData?.assignments?.no_of_items;
      const sortedData = filteredVideoResult.resources?.sort(
        (a, b) => a.module_order - b.module_order
      );
      setAllVideoResources(sortedData);
      const limitedResources = sortedData.slice(0, displayCount);
      console.log("filteredVideoResult.resources.length",filteredVideoResult.resources.length,displayCount)
      if(filteredVideoResult.resources.length > displayCount){
        setShowViewAll(true);
      }
      setFilteredVideoResources(limitedResources as AllResourceResponse[])
     
      //setAllDocuments(fileResult as AllResourceResponse[]);
    } catch (error) {
      setIsLoading(true);
    }
  };
  const closeDialog = (): void => {
    setViewAllResource(false);
  };
  return (
    <div className="mt-2">
      <div className="flex items-center">
            <h2 className="text-2xl text-[var(--color-font-color)] font-bold">
            Assignments
            </h2>
            {showViewAll && (
            <button
              className="text-[var(--color-nav-text)] font-semibold underline text-base ml-2 mt-1 hover:text-[var(--color-nav-hover-text)] transition"
              onClick={() => setViewAllResource(true)}
            >
              (View All)
            </button>
            
            )}
        </div>
        
        {isLoading ? (
        <ViewResources
          resources={filteredVideoResources}
          title="Assignments"
          isModal={isModal ?? false}
          hideProgress={false}
        />
      ) : (
        <Spinner></Spinner>
      )}
      {!hideExam && (
        <div className="mt-4">
          <h2 className="text-2xl text-[#FB8500] font-bold">Practices</h2>
          <div
            className="border border-gray bg-slate-200 rounded-xl bg-white p-2 flex flex-col items-center cursor-pointer w-full max-w-[380px]  max-h-[380px] mt-2" // Use max-width and center on mobile
            onClick={() =>
              router.push(`/pages/exams-list?course_id=${courseID}`)
            }
          >
            <div className="w-full flex items-center justify-center overflow-hidden rounded-lg">
              <img
                src="/assets/exams.jpg"
                className="object-cover h-auto w-full"
                alt="Thumbnail"
              />
            </div>

            <div className="w-full h-[1px] bg-violet-500 my-3"></div>
            <div className="text-lg font-semibold text-gray-700 text-center">
              {" "}
              {/* Center text */}
              <BookOpen className="text-violet-500 mx-auto" />{" "}
              {/* Center icon */}
              <h4 className="text-lg font-semibold text-black mb-1">Exams</h4>
            </div>
          </div>
        </div>
      )}
      {viewAllResource && (
              <Modal
                title="Assignments"
                header=""
                openDialog={viewAllResource}
                closeDialog={closeDialog}
                type="max-w-3xl"
              >
                <ViewAllResourceModal
                  closeDialog={closeDialog}
                  isGeneralResource={false}
                  AllVideoResources={AllVideoResources}
                  CourseId={courseID}
                />
              </Modal>
            )}
    </div>
  );
};

export default ResourcesList;
