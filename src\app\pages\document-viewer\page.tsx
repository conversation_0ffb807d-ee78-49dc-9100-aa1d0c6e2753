"use client";

import React, { useEffect, useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { useSearchParams } from "next/navigation";
import { useRouter } from "next/navigation";
import MainLayout from "../layouts/mainLayout";
import CommentsSection from "@/components/comments/comments";
import { KEYS } from "@/lib/keys";
import ReactGoogleSlides from "react-google-slides";
import { Modal } from "@/components/ui/modal";
import CheckPointModal from "@/components/checkPointModal";
import { useExam } from "@/hooks/useExam";
import {
  CheckPoint,
  CommentsResponse,
  CourseData,
  ErrorCatch,
  InnerItem,
  LoginUserData,
  ToastType,
  UpdateDocumentRequest,
} from "@/types";
import { useToast } from "@/components/ui/use-toast";
import { getLocalStorageItem } from "@/lib/utils";
import dynamic from "next/dynamic";
import { ERROR_MESSAGES, SUCCESS_MESSAGES } from "@/lib/messages";
import { Card } from "@/components/ui/card";
import {
  CheckCircle,
  CommandIcon,
  FlagIcon,
  Loader2,
  MessageCircle,
  MessageSquarePlus,
  PresentationIcon,
  SquarePen,
  ThumbsUp,
} from "lucide-react";
import useComments from "@/hooks/useComments";
import CommentsModal from "@/components/comment-modal";
import { Spinner } from "@/components/ui/progressiveLoder";
import NextBreadcrumb from "@/components/breadcrumb";
import getBreadCrumbItems from "@/hooks/useBreadcrumbs";
import { pptCheckpointAlert, UUID } from "@/lib/constants";
import { useCourse } from "@/hooks/useCourse";
import { UseLogClass } from "@/hooks/useLog";

const PPTViewer = (): React.JSX.Element => {
  const [activeTab, setActiveTab] = useState("presentation");

  const ReactGoogleSlides = dynamic(() => import("react-google-slides"), {
    ssr: false,
    loading: () => <LoadingSpinner />,
  });
  const searchParams = useSearchParams();
  const router = useRouter();
  const getResourceData = getLocalStorageItem(KEYS.RESOURCE_DATA);

  const parsedData = getResourceData ? JSON.parse(getResourceData) : {};
  const { toast } = useToast() as unknown as ToastType;
  const fileName = parsedData.name;
  const topicName = parsedData.topic_name;
  const sourceUrl = parsedData.url || parsedData.external_url || parsedData.file_url;
  const parsedUrl = sourceUrl ? new URL(sourceUrl) : null;
  const fileId = parsedUrl?.pathname.split("/d/")[1]?.split("/")[0];
  const fileUrl = fileId ? `https://docs.google.com/presentation/d/${fileId}/embed?start=false&rm=minimal` : sourceUrl;
  const instance_id = parsedData.id;
  const is_checkpoint_enabled = parsedData.is_checkpoint_enabled;
  const course_module_id = parsedData.course_module_id;
  const section = searchParams?.get("section_id") as string;
  const courseId = searchParams?.get("course_id") as string;
  const progress = searchParams?.get("progress");
  const exam_result = searchParams?.get("result") ?? "";
  const { getCheckPointsdetails, startCheckPointQuiz } = useExam();

  const [sectionId, setSectionId] = useState<string>("");
  const [currentSlide, setCurrentSlide] = useState<number>(
    parseInt(getLocalStorageItem("currentSlide") || "1")
  );
  const { addComments, getComments } = useComments();
  const [checkpointReached, setCheckpointReached] = useState(false);
  const [commentsExist, setCommentsExist] = useState(false);
  const [checkPointData, setcheckPointData] = useState<CheckPoint[]>([]);
  const [checkPoint, setCheckPoint] = useState<CheckPoint | null>(null);
  const totalSlides = Number(searchParams?.get("page_count")) ?? 0;
  const [commentsData, setCommentsData] = useState<CommentsResponse[]>([]);
  const [commentCount, setCommentCount] = useState<number>(0);
  const [courseData, setCourseData] = useState<CourseData[]>([]);
  const [openFeedback, setOpenFeedback] = useState(false);
  const [isCommentsLoding, setIsCommentsLoading] = useState<boolean>(true);
  const { updateDocumentProgress } = useCourse();
  const topicWise = searchParams?.get("is_topic_wise");
  const [checkPointsLength, setcheckPointDataLength] = useState<CheckPoint[]>(
    []
  );
  const [likeCount, setLikeCount] = useState(0);
  const [LikeData, setLikeData] = useState<CommentsResponse[]>([]);

  const [isLoading, setIsLoading] = useState<boolean>(true);
  const visitedCheckpoints = JSON.parse(
    getLocalStorageItem(KEYS.VISITED_CHECKPOINTS) || "[]"
  );
  const [breadcrumbItems, setBreadcrumbItems] = useState<InnerItem[]>([]);
  const [nextCheckpoint, setNextCheckpoint] = useState<CheckPoint[]>([]);
  const [showComments, setShowComments] = useState(false);
  const [isLiked, setIsLiked] = useState(false);
  const { insertLogDetails } = UseLogClass();
  const [activityType, setActivityType] = useState<string>("");
  const [userId, setUserId] = useState<string>("");

  useEffect(() => {
    const USER_DATA = localStorage.getItem(KEYS.USER_DETAILS);
    if (USER_DATA !== null) {
      const userInfo = JSON.parse(USER_DATA);
      setUserId(userInfo.id);
    }
    const courses = getLocalStorageItem("courseData");
    if (courses) {
      const parsedData = JSON.parse(courses as string);
      setCourseData(parsedData);
    }
    getLikeData();
    getCommentData();
    checkUserLiked();
    if (topicWise === "true") {
      setBreadcrumbItems(
        getBreadCrumbItems("Document Viewer Topicwise", {
          section_id: section as string,
          course_id: courseId,
        })
      );
    } else {
      setBreadcrumbItems(
        getBreadCrumbItems("Document Viewer", {
          course_id: courseId,
        })
      );
    }

    insertLogDetails(
      "Course_Resource",
      "Resource Viewer",
      `${fileName} PPT Viewed `,
      "SUCCESS",
      instance_id
    );
  }, []);

  // to restrict browser back
  useEffect(() => {
    window.history.pushState(null, "", window.location.href);
    const handlePopState = () => {
      window.history.pushState(null, "", window.location.href);
    };
    window.addEventListener("popstate", handlePopState);
    return () => {
      window.removeEventListener("popstate", handlePopState);
    };
  }, [router]);

  const getLikeData = () => {
    const fetchLikeData = async (): Promise<void> => {
      const USER_DATA = localStorage.getItem(KEYS.USER_DETAILS);
    let currentUserId = "";
    if (USER_DATA !== null) {
      const userInfo = JSON.parse(USER_DATA);
      currentUserId = userInfo.id;
    }

      const org_id = localStorage.getItem(KEYS.ORG_ID);
      const params = {
        instance_id: instance_id as string,
        org_id: org_id as string,
        activity_type: "like",
      };
      try {
        const result = await getComments(params);

        setIsCommentsLoading(false);
        if (result.length > 0) {
          setLikeCount(result.length);
          setLikeData(result);
          const userHasLiked = result.some(
            (like) => like.user_id === currentUserId && like.activity_type === "like"
          );
          setIsLiked(userHasLiked);
        }
      } catch (error) {
        console.log(error);
      }
    };
    fetchLikeData()
      .then((response) => {
        console.log(response);
      })
      .catch((error) => {
        console.log(error);
      });
  };

  const getCommentData = () => {
    const fetchCommentData = async (): Promise<void> => {
      const org_id = localStorage.getItem(KEYS.ORG_ID);
      const params = {
        instance_id: instance_id as string,
        org_id: org_id as string,
        activity_type: "comment",
      };
      try {
        const result = await getComments(params);

        setIsCommentsLoading(false);
        if (result) {
          setCommentCount(result.length);
          setCommentsData(result);
        }
      } catch (error) {
        console.log(error);
      }
    };
    fetchCommentData()
      .then((response) => {
        console.log(response);
      })
      .catch((error) => {
        console.log(error);
      });
  };

  useEffect(() => {
    setSectionId(section as string);
    const data = {
      fileName,
      fileUrl,
      instance_id,
      is_checkpoint_enabled,
      course_module_id,
    };

    localStorage.setItem("storedData", JSON.stringify(data));

    const fetchCheckpointsAndCheckSlide = async () => {
      try {
        if (is_checkpoint_enabled) {
          const checkPointData = await getCheckPointsdetails(
            course_module_id as string
          );
          const remainingCheckpoints = checkPointData.check_points
            ?.filter((cp) => !cp.attended || cp.status !== "PASSED")
            .sort((a, b) => a.sequence - b.sequence);
          if (
            remainingCheckpoints.length === 0 &&
            currentSlide === totalSlides
          ) {
            updateProgress(totalSlides);
          }
          if (currentSlide !== totalSlides) {
            updateProgress(currentSlide);
          }
          setNextCheckpoint(remainingCheckpoints);
          setcheckPointDataLength(remainingCheckpoints);
          const checkpoint = remainingCheckpoints.find(
            (point) => point.start_page === currentSlide
          );
          if (checkpoint) {
            if (!visitedCheckpoints.includes(currentSlide)) {
              setCheckPoint(checkpoint);
              setCheckpointReached(true);
            }
          } else {
            setCheckpointReached(false);
          }
        } else {
          updateProgress(currentSlide);
        }
      } catch (error) {
        console.error("Error:", error);
      }
    };

    fetchCheckpointsAndCheckSlide();
  }, [currentSlide]);

  useEffect(() => {
    if (exam_result === "FAILED") {
      setButtonStatus();
      updateProgress(currentSlide - 1);
    }
    if (exam_result === "PASSED") {
      updateProgress(currentSlide);
    }
  }, [exam_result]);

  const handleComments = (): void => {
    getCommentData();
    setShowComments(!showComments);
  };

  const setButtonStatus = () => {
    setCurrentSlide((prevSlide) => {
      const previousSlide = currentSlide - 1;
      localStorage.setItem(KEYS.CURRENT_SLIDE, previousSlide.toString());
      const updatedVisitedCheckpoints = visitedCheckpoints.filter(
        (slide: number) => slide !== prevSlide + 1
      );
      localStorage.setItem(
        KEYS.VISITED_CHECKPOINTS,
        JSON.stringify(updatedVisitedCheckpoints)
      );
      const checkpoint = checkPointData.find(
        (point) => point.start_page === previousSlide
      );
      if (checkpoint && !updatedVisitedCheckpoints.includes(previousSlide)) {
        setCheckPoint(checkpoint);
        setCheckpointReached(true);
      } else {
        setCheckpointReached(false);
      }

      return previousSlide;
    });
  };

  const attendLater = (): void => {
    setcheckPointData((prevCheckPoints) =>
      prevCheckPoints.filter(
        (cp) => cp.checkpoint_id !== checkPoint?.checkpoint_id
      )
    );
  };
  const handleNextSlide = () => {
    setCurrentSlide((nextSlide) => {
      const newSlide = nextSlide < totalSlides ? nextSlide + 1 : nextSlide;
      localStorage.setItem(KEYS.CURRENT_SLIDE, newSlide.toString());
      const checkpoint = checkPointData.find(
        (point) => point.start_page === newSlide
      );
      if (checkpoint && !visitedCheckpoints.includes(newSlide)) {
        setCheckPoint(checkpoint);
        setCheckpointReached(true);
      } else {
        setCheckpointReached(false);
      }
      return newSlide;
    });
  };

  const handlePrevSlide = () => {
    setCurrentSlide((prevSlide) => {
      const newSlide = prevSlide > 1 ? prevSlide - 1 : prevSlide;
      localStorage.setItem(KEYS.CURRENT_SLIDE, newSlide.toString());
      return newSlide;
    });
  };
  const closeDialog = async (checkpoint: CheckPoint) => {
    try {
      const currentTime = new Date();
      const currentTimeString = formatDateToString(currentTime);
      const org_id = getLocalStorageItem(KEYS.ORG_ID);
      const USER_DATA = getLocalStorageItem("userDetails");

      if (USER_DATA !== null && USER_DATA !== undefined) {
        const userInfo = JSON.parse(USER_DATA) as LoginUserData;
        const params = {
          checkpoint_id: checkpoint?.checkpoint_id as string,
          user_start_time: currentTimeString,
          org_id: org_id ?? "",
          user_id: userInfo.id,
        };
        const quizData = await startCheckPointQuiz(params);
        localStorage.setItem(KEYS.QUIZ_DATA, JSON.stringify(quizData));

        const updatedCheckpoints = [...visitedCheckpoints, currentSlide];
        localStorage.setItem(
          KEYS.VISITED_CHECKPOINTS,
          JSON.stringify(updatedCheckpoints)
        );
        localStorage.removeItem(KEYS.REMAINING_TIME);
        router.push(
          `/pages/exam-view?checkpoint_exam=${true}&isDocument=${true}&section_id=${sectionId}&page_count=${totalSlides}`
        );
        insertLogDetails(
          "Course_Resource",
          "Resource Viewer",
          `Checkpoint exam start`,
          "SUCCESS",
          checkPoint?.checkpoint_id ?? null
        );
      }
    } catch (error) {
      const err = error as ErrorCatch;
      toast({
        variant: "destructive",
        title: ERROR_MESSAGES.error,
        description: err?.message,
      });
      insertLogDetails(
        "Course_Resource",
        "Resource Viewer",
        `Checkpoint exam start`,
        "ERROR",
        checkPoint?.checkpoint_id ?? null
      );
    }
    setCheckpointReached(false);
  };

  const formatDateToString = (date: Date): string => {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, "0");
    const day = String(date.getDate()).padStart(2, "0");
    const hours = String(date.getHours()).padStart(2, "0");
    const minutes = String(date.getMinutes()).padStart(2, "0");
    const seconds = String(date.getSeconds()).padStart(2, "0");
    const milliseconds = String(date.getMilliseconds()).padStart(3, "0");

    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}.${milliseconds}`;
  };

  const handleCancel = () => {
    localStorage.removeItem(KEYS.VISITED_CHECKPOINTS);
    localStorage.removeItem(KEYS.CURRENT_SLIDE);

    if (topicWise === "true") {
      // Check if we came from subject-details page
      const resourceData = localStorage.getItem(KEYS.RESOURCE_DATA);
      if (resourceData) {
        const parsedResourceData = JSON.parse(resourceData);
        // If the resource data has topic_name, it likely came from subject-details
        if (parsedResourceData.topic_name) {
          router.push(
            `/pages/subject-details?section_id=${sectionId}&course_id=${courseId}`
          );
          return;
        }
      }
      router.push(
        `/pages/course-resource?section_id=${sectionId}&course_id=${courseId}`
      );
    } else {
      if (process.env.NEXT_PUBLIC_SUBJECT_WISE_LIST === "true") {
        router.push(`/pages/section-details`);
      } else {
        router.push(`/pages/course-details?course_id=${courseId}`);
      }
    }
  };
  const addFeedback = () => {
    setActivityType("comment");
    setOpenFeedback(true);
  };

  const closeFeedback = () => {
    setOpenFeedback(false);
    getCommentData();
  };
  const handleCheckpointClick = (checkpoint: CheckPoint) => {
    setCheckPoint(checkpoint);
    setCheckpointReached(true);
  };

  const updateProgress = async (curresnt_slide: number) => {
    const orgID = getLocalStorageItem(KEYS.ORG_ID);
    const userID = getLocalStorageItem(KEYS.USER_ID);
    let params: UpdateDocumentRequest = {
      course_id: courseId as string,
      instance_id: instance_id,
      org_id: orgID as string,
      progress_data: { current_page: curresnt_slide },
      user_id: userID as string,
    };
    try {
      const result = await updateDocumentProgress(params);
      if (result) {
        if (currentSlide === totalSlides) {
          try {
            await insertLogDetails(
              "Course_Resource",
              "Resource Viewer",
              `${fileName} PPT completed `,
              "SUCCESS",
              instance_id
            );
          } catch (error) {
            console.log(error);
          }

          toast({
            variant: "default",
            title: SUCCESS_MESSAGES.success,
            description: SUCCESS_MESSAGES.progress_msg,
          });
        }
        //  handleCancel();
      }
    } catch (error) {
      const err = error as ErrorCatch;
      await insertLogDetails(
        "Course_Resource",
        "Resource Viewer",
        `${fileName} PPT failed to complete`,
        "ERROR",
        instance_id
      );
      // toast({
      //   variant: "destructive",
      //   title: ERROR_MESSAGES.error,
      //   description: err?.message,
      // });
    }
  };

  const handlePreviousSlide = () => {
    if (currentSlide > 1) {
      setCurrentSlide(currentSlide - 1);
    }
  };

  const handleSlideClick = () => {
    handleNextSlide();
  };

  const checkUserLiked = () => {
    const userHasLiked = LikeData.some(
      (like) => like.user_id === userId && like.activity_type === "like"
    );

    setIsLiked(userHasLiked);
  };

  const handleLike = async () => {
    const reqParams = {
      comment_data: {
        subject: "",
        message: "",
        type: "Feedback",
        parent_id: null,
        activity_type: "like",
      },
      instance_id: instance_id,
      user_id: userId,
    };

    try {
      const result = await addComments(reqParams);

      if (result.status === "success") {
        toast({
          variant: "success",
          title: isLiked ? "Unliked!" : "Liked!",
          description: isLiked
            ? "You removed your like."
            : "You liked this material.",
        });
        setIsLiked(!isLiked);
        setLikeCount((prevCount) => (isLiked ? prevCount - 1 : prevCount + 1));
        await insertLogDetails(
          "Course_Resource",
          "Resource Viewer",
          `${fileName} PPT Liked `,
          "SUCCESS",
          result.comment_id
        );
      }
    } catch (error) {
      toast({
        variant: "destructive",
        title: "Error",
        description: "Action failed.",
      });
      await insertLogDetails(
        "Course_Resource",
        "Resource Viewer",
        `Failed to like ${fileName} PPT `,
        "ERROR",
        UUID
      );
    }
  };
  return (
    <MainLayout titleText="">
      <NextBreadcrumb
        items={breadcrumbItems}
        separator={<span> | </span>}
        containerClasses="flex py-5"
        listClasses="hover:underline mx-2 font-bold"
        capitalizeLinks
      />
      <Card className="w-full sm:w-4/5 lg:w-1/2 max-w-full mx-auto p-4 sm:p-6 text-[var(--color-font-color)]">
        <div className="flex flex-col">
          <div className="mt-2">
            <div
              role="tabpanel"
              id="panel-presentation"
              aria-labelledby="presentation"
              hidden={activeTab !== "presentation"}
              className="focus:outline-none"
            >
              <div className="w-full  mb-2">
                <p className="text-lg font-semibold  mb-2">
                  Topic: {topicName}
                </p>
                <p className="text-lg font-semibold  mb-2">Name: {fileName}</p>
              </div>
              <div className="space-y-4 bg-gray-50 rounded-lg">
                <div className="w-full sm:p-2 relative">
                  <div className="overflow-x">
                    {/* <ReactGoogleSlides
                      slidesLink={fileUrl}
                      position={currentSlide}
                      loop
                      width="100%"
                      height={600}
                      showControls={true}
                      onChange={changeFn}
                    /> */}

                    <div>
                      <div
                        className="relative w-full"
                        style={{ paddingTop: "56.25%" }}
                      >
                        <div className="absolute inset-0">
                          <div className="relative w-full h-full">
                            <iframe
                              src={fileUrl + `&slide=${currentSlide}`}
                              className="w-full h-full"
                              allowFullScreen
                              title="Google Slides"
                            />
                            {/* Invisible overlay */}
                            <div
                              className="absolute inset-0 cursor-pointer"
                              onClick={handleSlideClick}
                            />
                          </div>
                        </div>
                      </div>
                    </div>
                    <div className="mt-2 flex items-center justify-center gap-4">
                      <button
                        onClick={handlePrevSlide}
                        disabled={currentSlide === 1}
                        className={`px-4 py-2 rounded-lg text-white font-semibold transition-all ${
                          currentSlide === 1
                            ? "bg-gray-400 cursor-not-allowed"
                            : "bg-blue-500 hover:bg-blue-600"
                        }`}
                      >
                        Previous
                      </button>

                      <span className="text-lg font-medium">
                        Slide {currentSlide} of {totalSlides}
                      </span>

                      <button
                        onClick={handleNextSlide}
                        disabled={currentSlide === totalSlides}
                        className={`px-4 py-2 rounded-lg text-white font-semibold transition-all ${
                          currentSlide === totalSlides
                            ? "bg-gray-400 cursor-not-allowed"
                            : "bg-blue-500 hover:bg-blue-600"
                        }`}
                      >
                        Next
                      </button>
                    </div>

                    {progress != "100" && nextCheckpoint.length > 0 && (
                      <div className="mt-4 flex flex-wrap gap-4 justify-center">
                        <div className="flex items-center">
                          <p className="text-sm font-bold text-red-700">
                            {pptCheckpointAlert}
                          </p>
                        </div>

                        {/* <Button
                          onClick={() => handleCheckpointClick(nextCheckpoint[0])}
                          variant="outline"
                          className="mr-2 bg-orange-500 text-white font-normal rounded-md px-4 py-2 hover:bg-orange-600 transition duration-300"
                        >
                          Begin Assessment -  {nextCheckpoint[0]?.checkpoint_name}
                        </Button> */}
                      </div>
                    )}

                    {/* <div className="flex justify-between items-center mt-2"> */}
                    {/* <Button
                        onClick={handlePrevSlide}
                        disabled={currentSlide === 1}
                        variant="outline"
                        className="mr-2"
                      >
                        Previous
                      </Button>

                      <div className="text-center w-full">
                        Slide {currentSlide} of {totalSlides}
                      </div>

                      <Button
                        onClick={handleNextSlide}
                        disabled={currentSlide === totalSlides}
                        className="ml-2 bg-[#9FC089]"
                      >
                        Next
                      </Button> */}
                    {/* </div> */}
                    <style jsx>{`
                      iframe {
                        border: none;
                      }
                      button {
                        padding: 10px 15px;
                        margin: 5px;
                        font-size: 16px;
                      }
                    `}</style>
                  </div>

                  <p className="text-lg mt-2">{parsedData.description}</p>
                  {/* {checkPointData.length > 0 && (
                    <p className="text-lg text-red-500">
                      {ERROR_MESSAGES.checkpint_file_msg}
                    </p>
                  )} */}
                </div>

                {/* {progress != "100" &&
                  checkPointsLength.length === 0 &&
                  !is_checkpoint_enabled && (
                    <div className="flex justify-end">
                      <button
                        onClick={() => updateProgress(totalSlides)}
                        className="flex items-center px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700"
                      >
                        <CheckCircle className="mr-2" size={20} />{" "}
                      
                        Mark as Completed
                      </button>
                    </div>
                  )} */}
              </div>
              <div className="flex justify-between items-center mt-4 mb-4">
                <div className="flex items-center gap-2 ">
                  <button
                    onClick={handleLike}
                    className="flex items-center gap-2 text-black-600 bg-gray-100 p-2 rounded transition"
                    title={isLiked ? "Unlike" : "Like"}
                  >
                    <ThumbsUp
                      className={`w-5 h-5 ${
                        isLiked
                          ? "text-blue-600 fill-blue-600"
                          : "text-gray-600"
                      }`}
                      fill={isLiked ? "currentColor" : "none"}
                    />
                    <span className="text-sm font-medium text-[var(--color-font-color)]">
                      {likeCount} {likeCount === 1 ? "Like" : "Likes"}
                    </span>
                  </button>
                </div>

                <button
                  onClick={handleComments}
                  className="flex items-center gap-2 text-black-600 bg-gray-100 p-2 rounded transition"
                >
                  <MessageCircle className="w-5 h-5 text-orange-600" />
                  <span className="text-sm font-medium text-[var(--color-font-color)]">
                    Comments
                  </span>
                </button>
              </div>
              {showComments && (
                <div>
                  <div className="flex justify-between items-center gap-2 mb-2 mt-5">
                    <div className="flex items-center gap-2">
                      <MessageCircle className="w-5 h-5 text-orange-600" />
                      <h2 className="text-xl font-semibold">Comments</h2>
                    </div>
                    <div className="cursor-pointer" title="Add comments">
                      <MessageSquarePlus
                        color="orange"
                        size={24}
                        onClick={addFeedback}
                      />
                    </div>
                  </div>

                  {!isCommentsLoding ? (
                    <div className="space-y-4 p-4 bg-gray-50 rounded-lg">
                      <CommentsSection
                        id={instance_id}
                        commentsData={commentsData}
                        onCommentAdded={getCommentData}
                      ></CommentsSection>
                    </div>
                  ) : (
                    <Spinner></Spinner>
                  )}
                </div>
              )}
              {checkpointReached && (
                <Modal
                  title=""
                  header=""
                  openDialog={checkpointReached}
                  closeDialog={() => {
                    setCheckpointReached(false), attendLater();
                  }}
                  type="max-w-3xl"
                >
                  <CheckPointModal
                    closeDialog={() => {
                      checkPoint ? closeDialog(checkPoint) : "";
                    }}
                    cancelCheckPoint={() => {
                      setCheckpointReached(false), attendLater();
                    }}
                  />
                </Modal>
              )}
              {openFeedback && (
                <Modal
                  title="Add Feedback/Suggestion"
                  header=""
                  openDialog={openFeedback}
                  closeDialog={() => {
                    closeFeedback();
                  }}
                  type="max-w-xl"
                >
                  <CommentsModal
                    closeDialog={() => {
                      closeFeedback();
                    }}
                    instanceId={instance_id}
                  />
                </Modal>
              )}
            </div>

            {/* <div
              role="tabpanel"
              id="panel-commentss"
              aria-labelledby="comments"
              hidden={activeTab !== "comments"}
              className="focus:outline-none"
            >
              <div className="flex items-center gap-2 mb-4">
                <CommandIcon className="w-5 h-5 text-orange-600" />
                <h2 className="text-xl font-semibold">Comments</h2>
              </div>
              <div className="space-y-4 p-4 bg-gray-50 rounded-lg">
                <CommentsSection id={instance_id} commentsData = {commentsData}/>
                <CommentsSection id={instance_id} commentsData = {commentsData}/>
              </div>
            </div> */}

            {/* <div
            role="tabpanel"
            id="panel-settings"
            aria-labelledby="settings"
            hidden={activeTab !== 'settings'}
            className="focus:outline-none"
          >
            <div className="flex items-center gap-2 mb-4">
              <Settings className="w-5 h-5 text-orange-600" />
              <h2 className="text-xl font-semibold">Settings</h2>
            </div>
            <div className="space-y-4 p-4 bg-gray-50 rounded-lg">
              <p className="text-gray-600">
                Configure your project settings here.
              </p>
            </div>
          </div> */}
          </div>
        </div>
      </Card>
      <div className="flex justify-end mt-4 sticky bottom-2 px-2 ">
        <Button variant="outline" className="rounded-md" onClick={handleCancel}>
          Back
        </Button>
      </div>
      {/* <div className="relative border rounded-lg p-4 mb-4 w-full mt-4">
        <div className="flex justify-between pr-4 align-middle cursor-pointer">
          <div className="text-xl font-semibold pl-2 pb-2">{fileName}</div>
        </div>
        <div className="flex flex-col lg:flex-row h-full">
          <div className="w-full lg:w-3/4 p-2 sm:p-4 relative">
            <div className="overflow-x">
              <ReactGoogleSlides
                slidesLink={fileUrl}
                position={currentSlide}
                loop
                width="100%"
                height={700}
                
              />
              <div className="flex justify-between items-center mt-2">
                <Button
                  onClick={handlePrevSlide}
                  disabled={currentSlide === 1}
                  variant="outline"
                  className="mr-2"
                >
                  Previous
                </Button>

                <div className="text-center w-full">
                  Slide {currentSlide} of {totalSlides}
                </div>

                <Button
                  onClick={handleNextSlide}
                  disabled={currentSlide === totalSlides}
                  className="ml-2 bg-[#9FC089]"
                >
                  Next
                </Button>
              </div>
            </div>
            <p className="text-lg mt-2">{parsedData.description}</p>
            { checkPointData.length>0  && ( <p className="text-lg text-red-500">{ERROR_MESSAGES.checkpint_file_msg}</p>)}
          </div>

          {commentsExist && (
            <div className="w-full lg:w-1/2 h-full border rounded-md p-2 sm:p-4 mt-4 lg:mt-0">
              <p>test</p>
              <CommentsSection id={instance_id} />
            </div>
          )}
        </div>
      </div> */}

      {/* <div className="flex justify-end mt-4">
        <Button variant="outline" onClick={handleCancel}>
          Back
        </Button>
      </div> */}

      {/* {checkpointReached && (
        <Modal
          title=""
          header=""
          openDialog={checkpointReached}
          closeDialog={() => {
            setCheckpointReached(false), attendLater();
          }}
          type="max-w-3xl"
        >
          <CheckPointModal
            closeDialog={() => {
              checkPoint ? closeDialog(checkPoint) : "";
            }}
            cancelCheckPoint={() => {
              setCheckpointReached(false),attendLater();
            }}
          />
        </Modal>
      )} */}
    </MainLayout>
  );
};

const LoadingSpinner = () => {
  return (
    <div className="flex justify-center items-center h-full">
      <div className="animate-spin rounded-full h-12 w-12 border-t-4 border-blue-500 border-solid"></div>
      <p className="ml-4 text-lg font-semibold text-blue-500">
        <Loader2></Loader2>
      </p>
    </div>
  );
};
export default PPTViewer;
