"use client";
import React, { useEffect, useState, useCallback } from "react";
import MainLayout from "../layouts/mainLayout";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>ger, Ta<PERSON>Content } from "@/components/ui/tabs";
import { Separator } from "@/components/ui/separator";
import { Video, BookOpen, FileQuestion, FileText, File } from "lucide-react";
import { Button } from "@/components/ui/button";
import { useRouter, useSearchParams } from "next/navigation";
import { KEYS } from "@/lib/keys";
import { InnerItem, ViewResourcePageType } from "@/types";
import NextBreadcrumb from "@/components/breadcrumb";
import getBreadCrumbItems from "@/hooks/useBreadcrumbs";
import { Spinner } from "@/components/ui/progressiveLoder";
import "../../../styles/main.css";
import { useCourse } from "@/hooks/useCourse";
import VideoDialog from "@/components/ui/video-dialog";
import { Modal } from "@/components/ui/modal";
import DocumentDialog from "@/components/pptDialog";

interface ResourceItem {
  id: string;
  name: string;
  description: string;
  resource_type: string;
  module_type?: string; // Add original module_type for API calls
  progress: number;
  file_url?: string;
  module_id?: string;
  course_module_id?: string;
  external_url?: string;
  url?: string; // Add url property for viewer compatibility
  video_length?: string;
  section_name?: string;
  topic_name?: string; // Add topic_name for viewer compatibility
  extension?: string;
  page_count?: number;
  content?: string; // Add content property for HTML viewer
}

export default function SubjectDetails(): React.JSX.Element {
  const { sectionDetails, viewResourcePage } = useCourse();

  const [activeTab, setActiveTab] = useState("video");
  const [isLoading, setIsLoading] = useState(true);
  const [breadcrumbItems, setBreadcrumbItems] = useState<InnerItem[]>([]);
  const [videoResources, setVideoResources] = useState<ResourceItem[]>([]);
  const [studyMaterials, setStudyMaterials] = useState<ResourceItem[]>([]);
  const [practiceSets, setPracticeSets] = useState<ResourceItem[]>([]);

  const [viewOpen, setViewOpen] = useState(false);
  const [viewOpenPPT, setViewOpenPPT] = useState(false);
  const [url, setUrl] = useState("");
  const [pageCount, setPageCount] = useState<number>(0);
  const router = useRouter();
  const searchParams = useSearchParams();
  const sectionId = searchParams?.get("section_id") as string;
  const courseId = searchParams?.get("course_id") as string;

  const fetchData = useCallback(async (): Promise<void> => {
    try {
      setIsLoading(true);
      const response = await sectionDetails(
        sectionId as string,
        courseId as string
      );

      // Process the response data - handle new structure with folders and modules
      // Check if response is an array (new structure) or object (old structure)
      let sectionData: any;
      if (Array.isArray(response) && response.length > 0) {
        sectionData = response[0]; // Get first section data from array
      } else if (response && typeof response === 'object') {
        sectionData = response; // Use response directly if it's an object
      }

      if (sectionData) {
        console.log("Section Data:", sectionData);

        // Combine resources from folders and modules
        let allResources: any[] = [];

        // Add resources from folders
        if (sectionData.folders && sectionData.folders.length > 0) {
          console.log("Processing folders:", sectionData.folders.length);
          sectionData.folders.forEach((folder: any) => {
            if (folder.resources && folder.resources.length > 0) {
              console.log(`Folder "${folder.folder_name}" has ${folder.resources.length} resources`);
              allResources = [...allResources, ...folder.resources];
            }
          });
        }

        // Add resources from modules
        if (sectionData.modules && sectionData.modules.length > 0) {
          console.log("Processing modules:", sectionData.modules.length);
          allResources = [...allResources, ...sectionData.modules];
        }

        console.log("Total resources before deduplication:", allResources.length);

        // Remove duplicates based on instance ID
        const uniqueResources = allResources.filter((resource, index, self) =>
          index === self.findIndex((r) => r.instance === resource.instance)
        );

        console.log("Unique resources after deduplication:", uniqueResources.length);

        // Process videos for the video tab
        const videos = uniqueResources.filter(
          (module: any) => module.module_type === "Url"
        );
        console.log("Videos found:", videos.length);

        // Convert to ResourceItem format
        const videoResourceItems: ResourceItem[] = videos.map((video: any) => ({
          id: video.instance,
          name: video.module_name,
          description: `Duration: ${video.video_length || "Unknown"}`,
          resource_type: "video",
          module_type: video.module_type, // Store original module_type
          progress: video.progress,
          file_url: video.external_url,
          module_id: video.module_id,
          course_module_id: video.course_module_id,
          external_url: video.external_url,
          video_length: video.video_length,
          section_name: sectionData.name || "Topic 1",
          topic_name: sectionData.name || "Topic 1", // Add topic_name for viewer compatibility
        }));

        setVideoResources(videoResourceItems);

        // Process documents for study materials tab
        const documents = uniqueResources.filter(
          (module: any) =>
            module.module_type === "File" || module.module_type === "Page"
        );
        console.log("Documents found:", documents.length);

        // Convert to ResourceItem format
        const documentResourceItems: ResourceItem[] = documents.map(
          (doc: any) => ({
            id: doc.instance,
            name: doc.module_name,
            description: `Type: ${doc.extension || "Document"}`,
            resource_type: doc.extension || "document",
            module_type: doc.module_type, // Store original module_type
            progress: doc.progress,
            file_url: doc.external_url,
            module_id: doc.module_id,
            course_module_id: doc.course_module_id,
            external_url: doc.external_url,
            url: doc.external_url, // Add url property for viewer compatibility
            section_name: sectionData.name || "Topic 1",
            topic_name: sectionData.name || "Topic 1", // Add topic_name for viewer compatibility
            extension: doc.extension,
            page_count: doc.page_count,
            content: doc.content || "", // Add content for HTML viewer
          })
        );

        setStudyMaterials(documentResourceItems);

        // Process quizzes for practice sets tab
        const quizzes = uniqueResources.filter(
          (module: any) => module.module_type === "Quiz"
        );
        console.log("Quizzes found:", quizzes.length);

        // Convert to ResourceItem format
        const quizResourceItems: ResourceItem[] = quizzes.map((quiz: any) => ({
          id: quiz.instance,
          name: quiz.module_name,
          description: `Attempts remaining: ${quiz.attempts_remaining || 0}`,
          resource_type: "quiz",
          module_type: quiz.module_type, // Store original module_type
          progress: quiz.progress,
          module_id: quiz.module_id,
          course_module_id: quiz.course_module_id,
          section_name: sectionData.name || "Topic 1",
          topic_name: sectionData.name || "Topic 1", // Add topic_name for viewer compatibility
        }));

        setPracticeSets(quizResourceItems);
      }

      setIsLoading(false);
    } catch (error) {
      console.error("Error fetching subject data:", error);
      setIsLoading(false);
    }
  }, [sectionId, courseId, sectionDetails]);

  useEffect(() => {
    setBreadcrumbItems(
      getBreadCrumbItems("Subject Details", {
        course_id: courseId,
        subject_id: sectionId,
      })
    );
    fetchData();
  }, [courseId, sectionId]);

  const handleTabChange = (value: string) => {
    setActiveTab(value);
  };

  const isImageOrPdfUrl = (_url: string, item: ResourceItem): string => {
    if (item.extension === "pdf") {
      return "pdf";
    } else if (
      item.extension === "jpg" ||
      item.extension === "jpeg" ||
      item.extension === "png" ||
      item.extension === "gif" ||
      item.extension === "bmp"
    ) {
      return "image";
    } else if (
      item.extension === "ppt" ||
      item.extension === "pptx" ||
      item.extension === "doc" ||
      item.extension === "docx" ||
      item.extension === "xls" ||
      item.extension === "xlsx"
    ) {
      return "document";
    } else if (item.extension === "mp4") {
      return "video";
    } else {
      return "image";
    }
  };

  const openFileViewer = async (resource: ResourceItem) => {
    // Handle quiz/practice set resources - route to exams-list
    if (resource.resource_type === "quiz" || resource.module_type === "Quiz") {
      router.push(`/pages/exams-list?course_id=${courseId}&section_id=${sectionId}&from=subject-details`);
      return;
    }

    // First, call the viewResourcePage API to get detailed resource information
    const response = await viewResourcePage(
      resource.module_type || "File", // Use the original module_type from API
      resource.id,
      resource.course_module_id || ""
    );

    const resultData = response as ViewResourcePageType;
    const totalPages = resultData.page_count;

    // Store the API response data in localStorage (this has the correct structure for viewers)
    localStorage.setItem(KEYS.RESOURCE_DATA, JSON.stringify(resultData));

    if (resultData?.module_source === "File" || resultData?.module_source === "Document") {
      const fileTypeCheck = isImageOrPdfUrl(resultData?.url, resource);

      if (fileTypeCheck === "image") {
        router.push(`/pages/image-viewer?section_id=${sectionId}&is_topic_wise=${true}&course_id=${courseId}`);
      } else if (fileTypeCheck === "pdf") {
        router.push(
          `/pages/pdf-viewer?section_id=${sectionId}&page_count=${totalPages}&course_id=${courseId}&is_topic_wise=${true}`
        );
      } else if (fileTypeCheck === "document") {
        if (resource.progress < 100) {
          router.push(
            `/pages/document-viewer?section_id=${sectionId}&page_count=${totalPages}&course_id=${courseId}&progress=${resource.progress}&is_topic_wise=${true}`
          );
        } else {
          setPageCount(resource.page_count as number);
          setViewOpenPPT(true);
          setUrl(resource.external_url || "");
        }
      }
    } else if (resultData?.module_source === "Video") {
      if (resource.progress < 100) {
        router.push(
          `/pages/video-player?course_id=${courseId}&is_topic_wise=${true}&section_id=${sectionId}`
        );
      } else {
        setViewOpen(true);
        setUrl(resource.external_url || "");
      }
    } else {
      // Handle HTML and other types
      router.push(
        `/pages/html-viewer?course_id=${courseId}&progress=${resource.progress}&is_topic_wise=${true}&section_id=${sectionId}`
      );
    }
  };

  const closeDialog = () => {
    setViewOpen(false);
    setViewOpenPPT(false);
  };

  const handleBack = () => {
    router.push(`/pages/section-details?course_id=${courseId}`);
  };

  const getResourceIcon = (resourceType: string) => {
    switch (resourceType) {
      case "video":
        return <Video className="text-[#00afbb] w-8 h-8" />;
      case "quiz":
        return <FileQuestion className="text-[#00afbb] w-8 h-8" />;
      case "document":
      case "pdf":
      case "doc":
      case "docx":
      case "ppt":
      case "pptx":
        return <FileText className="text-[#00afbb] w-8 h-8" />;
      default:
        return <File className="text-[#00afbb] w-8 h-8" />;
    }
  };

  const renderResourceList = (resources: ResourceItem[]) => {
    if (resources.length === 0) {
      return <div className="text-center py-4">No resources available</div>;
    }

    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {resources.map((resource) => (
          <Card
            key={resource.id}
            className="cursor-pointer hover:shadow-lg transition-shadow duration-300"
            onClick={() => openFileViewer(resource)}
          >
            <CardContent className="p-4">
              <div className="flex flex-col h-full">
                <div className="flex items-center justify-center mb-3">
                  {getResourceIcon(resource.resource_type)}
                </div>
                <div className="text-center">
                  <h3
                    className="font-semibold text-lg mb-2 line-clamp-2"
                    title={resource.name}
                  >
                    {resource.name}
                  </h3>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  };

  return (
    <MainLayout titleText="">
      <NextBreadcrumb
        items={breadcrumbItems}
        separator={<span> | </span>}
        containerClasses="flex py-5"
        listClasses="hover:underline mx-2 font-bold"
        capitalizeLinks
      />

      {isLoading ? (
        <div className="flex justify-center items-center h-64">
          <Spinner />
        </div>
      ) : (
        <div className="w-full p-4">
          <h1 className="text-2xl font-bold mb-6">Subject Details</h1>

          <Tabs
            defaultValue="video"
            onValueChange={handleTabChange}
            className="w-full"
            value={activeTab}
          >
            <TabsList className="grid w-full grid-cols-3 h-16 bg-white mb-6">
              <TabsTrigger value="video">
                <div className="flex flex-col items-center">
                  <Video
                    className={
                      activeTab === "video" ? "text-[#00afbb]" : "text-black"
                    }
                  />
                  <span
                    className={
                      activeTab === "video" ? "text-[#00afbb]" : "text-black"
                    }
                  >
                    Video
                  </span>
                  <Separator
                    className={`${
                      activeTab === "video" ? "bg-[#00afbb]" : "bg-transparent"
                    }`}
                  />
                </div>
              </TabsTrigger>
              <TabsTrigger value="studyMaterial">
                <div className="flex flex-col items-center">
                  <BookOpen
                    className={
                      activeTab === "studyMaterial"
                        ? "text-[#00afbb]"
                        : "text-black"
                    }
                  />
                  <span
                    className={
                      activeTab === "studyMaterial"
                        ? "text-[#00afbb]"
                        : "text-black"
                    }
                  >
                    Study Material
                  </span>
                  <Separator
                    className={`${
                      activeTab === "studyMaterial"
                        ? "bg-[#00afbb]"
                        : "bg-transparent"
                    }`}
                  />
                </div>
              </TabsTrigger>
              <TabsTrigger value="practiceSet">
                <div className="flex flex-col items-center">
                  <FileQuestion
                    className={
                      activeTab === "practiceSet"
                        ? "text-[#00afbb]"
                        : "text-black"
                    }
                  />
                  <span
                    className={
                      activeTab === "practiceSet"
                        ? "text-[#00afbb]"
                        : "text-black"
                    }
                  >
                    Practice Set
                  </span>
                  <Separator
                    className={`${
                      activeTab === "practiceSet"
                        ? "bg-[#00afbb]"
                        : "bg-transparent"
                    }`}
                  />
                </div>
              </TabsTrigger>
            </TabsList>

            <TabsContent value="video" className="w-full">
              <Card className="w-full">
                <CardHeader className="bg-[#FDB666] text-white w-full">
                  <CardTitle>Video Resources</CardTitle>
                </CardHeader>
                <CardContent className="pt-6 w-full">
                  {renderResourceList(videoResources)}
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="studyMaterial" className="w-full">
              <Card className="w-full">
                <CardHeader className="bg-[#FDB666] text-white w-full">
                  <CardTitle>Study Materials</CardTitle>
                </CardHeader>
                <CardContent className="pt-6 w-full">
                  {renderResourceList(studyMaterials)}
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="practiceSet" className="w-full">
              <Card className="w-full">
                <CardHeader className="bg-[#FDB666] text-white w-full">
                  <CardTitle>Practice Sets</CardTitle>
                </CardHeader>
                <CardContent className="pt-6 w-full">
                  {renderResourceList(practiceSets)}
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>

          <div className="flex justify-end mt-6 sticky bottom-2 w-full">
            <Button
              variant="outline"
              className="rounded-md"
              onClick={handleBack}
            >
              Back
            </Button>
          </div>
        </div>
      )}

      {viewOpen && (
        <Modal
          title=""
          header=""
          openDialog={viewOpen}
          closeDialog={closeDialog}
          type="max-w-5xl"
        >
          <VideoDialog onCancel={closeDialog} url={url} />
        </Modal>
      )}

      {viewOpenPPT && (
        <Modal
          title=""
          header=""
          openDialog={viewOpenPPT}
          closeDialog={closeDialog}
          type="max-w-5xl"
        >
          <DocumentDialog
            onCancel={closeDialog}
            url={url}
            pageCount={pageCount}
          />
        </Modal>
      )}
    </MainLayout>
  );
}
