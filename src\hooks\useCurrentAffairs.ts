import { views } from "@/lib/apiConfig";
import { supabase } from "@/lib/client";
import { ORG_KEY } from "@/lib/utils";
import { KEYS } from "@/lib/keys";
import { currentAffairsResponse } from "@/types";

interface UseCurrentAffairReturn {
    currentAffairs: () => Promise<currentAffairsResponse[]>;
}
const useCurrentAffairs = (): UseCurrentAffairReturn => {
async function currentAffairs(
  ): Promise<currentAffairsResponse[]> {
    const orgId = localStorage.getItem(KEYS.ORG_ID) as string;
    try { 
      const currentAffair = views?.currentAffairs ?? "";
      const exeQuery = supabase
        .from(currentAffair)
        .select()
        .eq("org_id", orgId) .eq("type", "Current_Affairs");

      const { data, error } = await exeQuery;
      if (error) {
        throw new Error(error.message);
      }

      return data as currentAffairsResponse[];
      // }
    } catch (error) {
      console.error("Error:", error);
      throw error;
    }
  }
  return {
    currentAffairs,
  };
}
export default useCurrentAffairs;