// src/lib/timeUtils.ts
export const convertTimeStringToHours = (time: string): string => {
  const timeInSeconds = Number(time);
  if (!isNaN(timeInSeconds)) {
    const hours = Math.floor(timeInSeconds / 3600);
    const minutes = Math.floor((timeInSeconds % 3600) / 60);
    return `${hours} hrs ${minutes} min`;
  }
  
  if (typeof time !== "string" || !time.includes(":")) {
    console.error("Invalid input: expected a string in 'hh:mm:ss' format or seconds");
    return 'Invalid time';
  }
  
  const timeParts = time.split(":");
  if (timeParts.length !== 3) {
    console.error("Invalid time format: expected 'hh:mm:ss'");
    return 'Invalid time';
  }

  const [hoursStr, minutesStr, secondsStr] = timeParts;
  const hours = Number(hoursStr);
  const minutes = Number(minutesStr);
  const seconds = Number(secondsStr);

  if (isNaN(hours) || isNaN(minutes) || isNaN(seconds)) {
    console.error("Invalid time components: unable to convert to numbers");
    return 'Invalid time';
  }

  const totalMinutes = hours * 60 + minutes + Math.floor(seconds / 60);
  const resultHours = Math.floor(totalMinutes / 60);
  const resultMinutes = totalMinutes % 60;

  return `${resultHours} hrs ${resultMinutes} min`;
};
  
  export const timeToSeconds = (time: string) => {
    const [hours, minutes, seconds] = time.split(":").map(Number);
    return hours * 3600 + minutes * 60 + seconds;
  };
  
  export const secondsToTime = (totalSeconds: number) => {
    const hours = Math.floor(totalSeconds / 3600);
    const minutes = Math.floor((totalSeconds % 3600) / 60);
    const seconds = totalSeconds % 60;
    return `${String(hours).padStart(2, "0")}:${String(minutes).padStart(2, "0")}:${String(seconds).padStart(2, "0")}`;
  };
  
  export function convertDecimalHoursToHrMin(decimalHours: number) {
    const hours = Math.floor(decimalHours);
    const minutes = Math.floor((decimalHours - hours) * 60);
    return `${hours} hrs ${minutes} min`;
  }
  
  // Given decimal hours
  const decimalHours = 3.2827777777777776;
  
  // Convert and print the result
  const result = convertDecimalHoursToHrMin(decimalHours);
  console.log(result); // Output: "3 hrs 16 min"