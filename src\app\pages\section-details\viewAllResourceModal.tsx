"use client";
import { Button } from "@/components/ui/button";
import {
  convertDecimalHoursToHrMin,
  convertTimeStringToHours,
} from "@/lib/timeUtils";
import { AllResourceResponse, CourseStats } from "@/types";
import React, { useEffect } from "react";
import { Eye } from "lucide-react";
import { useRouter } from "next/navigation";
import { KEYS } from "@/lib/keys";
import ResourcesList from "../dashboard/resources";
import ViewResources from "../dashboard/viewResource";
import { useTranslation } from "next-i18next";
interface CourseStatsProps {
  closeDialog: () => void;
  CourseId: string | null;
  isMain?: boolean;
  title?: string;
  isGeneralResource?: boolean;
  AllVideoResources?: AllResourceResponse[];
}

export default function ViewAllResourceModal({
  isGeneralResource,
  closeDialog,
  AllVideoResources,
}: CourseStatsProps): React.JSX.Element {
  const router = useRouter();
  useEffect(() => {
    console.log("AllVideoResources ", AllVideoResources);
  }, []);
  const { t } = useTranslation("common");
  return (
    <div>
      <ViewResources
        resources={AllVideoResources as AllResourceResponse[]}
        title="Assignments"
        isModal={true}
        hideProgress={true}
        isGeneralResource={isGeneralResource}
      />
      <div className="flex justify-end mt-4">
        <Button variant="outline" className="" onClick={closeDialog}>
          {t("Close")}
        </Button>
      </div>
    </div>
  );
}
