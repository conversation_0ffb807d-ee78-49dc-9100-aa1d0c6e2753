"use client";
import MainLayout from "../layouts/mainLayout";
import "../../../styles/main.css";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { AlarmClock, CalendarPlus2 } from "lucide-react";
import { DATE_FORMAT } from "@/lib/constants";
import { useEffect, useState } from "react";
import { useExam } from "@/hooks/useExam";
import { useRouter, useSearchParams } from "next/navigation";
import { useTranslation } from "next-i18next";
import {
  ExamReviewType,
  InnerItem,
  ReviewAnswersType,
  SubmitAnswerType,
  SubmitQuizType,
  ToastType,
} from "@/types";
import { useToast } from "@/components/ui/use-toast";
import moment from "moment";
import { Spinner } from "@/components/ui/progressiveLoder";
import { KEYS } from "@/lib/keys";
import NextBreadcrumb from "@/components/breadcrumb";
import getBreadCrumbItems from "@/hooks/useBreadcrumbs";
export default function ExamReview(): React.JSX.Element {
  const { evaluateAnswer, calculateQuizGrade, submitAnswers } = useExam();
  const searchParams = useSearchParams();
  const quizAttemptId = searchParams?.get("quiz_attempt_id") as string;
  const quizId = searchParams?.get("quiz_id") as string;
  const courseId = searchParams?.get("course_id") as string;
  // const startDate = searchParams?.get("course_id") as string;
  const { toast } = useToast() as ToastType;
  const [examReview, setExamReview] = useState<ExamReviewType[]>([]);
  const router = useRouter();
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const isCheckpointExam = searchParams?.get("checkpoint_exam") === "true";
  const [startDate, setExamStartDate] = useState<string>("");
  const [breadcrumbItems, setBreadcrumbItems] = useState<InnerItem[]>([]);

  useEffect(() => {
    const examSubmittedDate = localStorage.getItem(KEYS.ATTENDED_DATE);
    setExamStartDate(examSubmittedDate as string);
    evaluateAnswers();

    setBreadcrumbItems(
      getBreadCrumbItems("Exam Review", {
        quiz_id: quizId,
        quiz_attempt_id: quizAttemptId,
        course_id: courseId,
        checkpoint_exam: isCheckpointExam,
      })
    );
  }, []);

  // to restrict browser back
  useEffect(() => {
    window.history.pushState(null, "", window.location.href);
    const handlePopState = () => {
      window.history.pushState(null, "", window.location.href);
    };
    window.addEventListener("popstate", handlePopState);
    return () => {
      window.removeEventListener("popstate", handlePopState);
    };
  }, [router]);

  const evaluateAnswers = async (): Promise<void> => {
    const passData = {
      quiz_id: quizId,
      quiz_attempt_id: quizAttemptId,
    };

    const fetchData = async (): Promise<void> => {
      try {
        const questionsData = await evaluateAnswer(passData as SubmitQuizType);
        setIsLoading(false);
        localStorage.setItem(
          KEYS.ANSWER_EVALUATION,
          JSON.stringify(questionsData)
        );
        const ansEvaluation = questionsData as ExamReviewType[];
        setExamReview(ansEvaluation);
      } catch (error: unknown) {}
    };
    fetchData().catch((error) => console.log(error));
  };
  const removeHTMLTags = (html: string | undefined): React.JSX.Element => {
    if (html != null) {
      const tempDiv = document.createElement("div");
      tempDiv.innerHTML = html;
      return <div dangerouslySetInnerHTML={{ __html: tempDiv.innerHTML }} />;
    } else {
      return <div></div>;
    }
  };
  const analyseExam = (): void => {
    localStorage.removeItem(KEYS.REMAINING_TIME);
    router.push(
      `/pages/examResultAnalysis?quiz_id=${quizId}&quiz_attempt_id=${quizAttemptId}&course_id=${courseId}&checkpoint_exam=${isCheckpointExam}`
    );
  };
  const goBackToList = (): void => {
    localStorage.removeItem(KEYS.REMAINING_TIME);
    router.push(`/pages/exams-list?course_id=${courseId}`);
  };
  const { t } = useTranslation("common");
  return (
    <MainLayout titleText="">
      <NextBreadcrumb
        items={breadcrumbItems}
        separator={<span> | </span>}
        containerClasses="flex py-5"
        listClasses="hover:underline mx-2 font-bold"
        capitalizeLinks
      />
      {isLoading ? (
        <Spinner />
      ) : (
        <div>
          <div className="flex justify-end">
            <Button
              variant="outline"
              className="rounded-md"
              onClick={goBackToList}
            >
              {t("Finish")}
            </Button>
          </div>
          <div className="text-base font-semibold pb-3 text-[var(--color-font-color)]">
            {examReview[0]?.name}
          </div>
          <div className="flex flex-col">
            <div className="grid gap-4 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-2 pb-4 pt-4 gap-8 ">
              <Card className="bg-[#FFF8F1] rounded-lg  p-4 bg-[#33363F]">
                <div className="flex flex-row gap-4">
                  <div>
                    <CalendarPlus2 className="text-white w-14 h-14 border border-2 p-2" />
                  </div>
                  <div className="flex flex-col">
                    <div className="text-white"> {t("Exam Attended on")}</div>
                    <div className="text-white text-base font-semibold">
                      {startDate}
                    </div>{" "}
                  </div>
                </div>
              </Card>
              <Card className="bg-[#FFF8F1] rounded-lg p-4 bg-[#33363F]">
                <div className="flex flex-row gap-4">
                  <div>
                    <AlarmClock className="text-white w-14 h-14 border border-2 p-2" />
                  </div>
                  <div className="flex flex-col">
                    <div className="text-white"> {t("Duration")}</div>
                    <div className="text-white text-base font-semibold">
                      {examReview[0]?.duration + " Minutes"}
                    </div>{" "}
                  </div>
                </div>
              </Card>
            </div>
            <div>
              <div className="text-base font-semibold pb-3">
                {t("Questions and selected answers")} :
              </div>
              {examReview[0]?.quest_answers?.map((question, index) => (
                <div key={question.question_id}>
                  <p className="mb-2 flex items-start ">
                    <span className="font-bold mr-5 text-base text-[var(--color-font-color)]">
                      {index + 1}.
                    </span>
                    {removeHTMLTags(question.question_text)}
                  </p>
                  <p className="ml-8 text-[var(--color-font-color)]">
                    {t("Answer opted")}:{" "}
                    {question.selected_answer_ids.length > 0
                      ? question.selected_answer_ids
                          .map((selectedAnswerId) => {
                            const selectedAnswer = question.answers?.find(
                              (answer: ReviewAnswersType) =>
                                answer.answer_id === selectedAnswerId
                            );

                            return selectedAnswer
                              ? selectedAnswer.answer
                              : "Unknown Answer";
                          })
                          .join(", ")
                      : "NA"}
                  </p>
                  <hr className="my-4 border-gray-300" />
                </div>
              ))}
            </div>
          </div>
          <div className="w-full grid justify-center p-10">
            <Button
              className="space-x-2 rounded-lg min-w-[350px] md:w-96 h-12"
              onClick={analyseExam}
              variant="default"
            >
              {" "}
              <p className="text-2xl"> {t("View Result")}</p>
            </Button>
          </div>
        </div>
      )}
    </MainLayout>
  );
}
