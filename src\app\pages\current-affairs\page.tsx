"use client";
import MainLayout from "../layouts/mainLayout";
import "../../../styles/main.css";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { FileText } from "lucide-react";
import { useRouter } from "next/navigation";
import useCurrentAffairs from "@/hooks/useCurrentAffairs";
import { useEffect, useState } from "react";
import React from "react";
import { currentAffairsResponse, InnerItem } from "@/types";
import Image from "next/image";
import { KEYS } from "@/lib/keys";
import NextBreadcrumb from "@/components/breadcrumb";
import getBreadCrumbItems from "@/hooks/useBreadcrumbs";
import { useTranslation } from "next-i18next";
export default function CurrentAffairs(): React.JSX.Element {
  const { t } = useTranslation("common");
  const router = useRouter();
  const { currentAffairs } = useCurrentAffairs();
  const [currentAffairData, setCurrentAffairData] = useState<
    currentAffairsResponse[]
  >([]);
  const [selectedMonth, setSelectedMonth] = useState<string>("");
  const [breadcrumbItems, setBreadcrumbItems] = useState<InnerItem[]>([]);
  // Function to generate last 12 months dynamically
  const getLast12Months = () => {
    const monthsArray = [];
    const currentDate = new Date();

    for (let i = 0; i < 12; i++) {
      const pastDate = new Date(
        currentDate.getFullYear(),
        currentDate.getMonth() - i,
        1
      );
      const monthName = pastDate.toLocaleString("default", { month: "long" });
      const year = pastDate.getFullYear();
      monthsArray.push({ month: monthName, year });
    }

    return monthsArray;
  };

  const months = getLast12Months();

  useEffect(() => {
    setBreadcrumbItems(getBreadCrumbItems("Current Affairs", {}));
    const fetchSessionData = async () => {
      try {
        const result = await currentAffairs();
        setCurrentAffairData(result);
      } catch (error) {
        console.error("Error fetching current affairs:", error);
      }
    };

    fetchSessionData();
    setSelectedMonth(months[0].month); // Set the current month as selected by default
  }, []);

  const currentDisplayData = (month: string) => {
    setSelectedMonth(month);
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return `${date.getDate()} ${date.toLocaleString("default", {
      month: "short",
    })} ${date.getFullYear()}`;
  };

  const getMonthNameFromDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleString("default", { month: "long" });
  };

  const filteredAffairs = currentAffairData?.filter(
    (item) => getMonthNameFromDate(item.publish_date) === selectedMonth
  );
  const handleCancel = () => {
    router.push(`/pages/dashboard`);
  };
  return (
    <MainLayout titleText="">
      <NextBreadcrumb
        items={breadcrumbItems}
        separator={<span> | </span>}
        containerClasses="flex py-5"
        listClasses="hover:underline mx-2 font-bold"
        capitalizeLinks
      />
      {/* Month Selection Buttons */}
      <div className="w-full overflow-x-auto whitespace-nowrap">
        {months.map(({ month, year }) => (
          <button
            key={`${month}-${year}`}
            onClick={() => currentDisplayData(month)}
            className={`inline-block m-2 p-2 bg-[#E7E7E7] text-[#423338] rounded-lg hover:bg-[#4cb5f54d] hover:text-[#00B9C7] hover:border hover:border-[#00B9C7] font-normal text-sm ${
              selectedMonth === month
                ? "bg-[#4cb5f54d] text-[#00B9C7] border border-[#00B9C7]"
                : ""
            }`}
          >
            {month} {year}
          </button>
        ))}
      </div>

      {/* Title Section */}
      {filteredAffairs.length > 0 && (
        <div className="text-base font-semibold p-3">
          {" "}
          {t("Add page title here")}
        </div>
      )}

      {/* Current Affairs Grid */}
      <div className="grid gap-4 sm:grid-cols-1 md:grid-cols-4 lg:grid-cols-4 pb-4 gap-8">
        {filteredAffairs.map((item) => (
          <Card className="rounded-lg p-4" key={item.title}>
            <div
              className="flex flex-row gap-4 cursor-pointer"
              onClick={() => {
                router.push(
                  `/pages/current-affair-details?title=${formatDate(
                    item.publish_date
                  )}`
                );

                if (
                  typeof window !== "undefined" &&
                  typeof localStorage !== "undefined"
                ) {
                  localStorage.setItem(
                    KEYS.CURRENT_AFFAIR_CONSTANT,
                    JSON.stringify({
                      title: item.title,
                      content: item.content,
                    }) ?? ""
                  );
                }
              }}
            >
              <FileText className="text-white w-16 h-16 border border-2 fill-[#FB8500] rounded-lg border-[#fb850036]" />
              <div className="flex flex-col">
                <div className="text-base font-semibold">
                  {formatDate(item.publish_date)}
                </div>
                <div className="text-sm font-base">{item.title}</div>
              </div>
            </div>
          </Card>
        ))}
      </div>
      <div className="flex justify-end mt-4 sticky bottom-2">
        <Button
          variant="outline"
          className="rounded-md "
          onClick={handleCancel}
        >
          {t("Back")}
        </Button>
      </div>
      {/* No Data Found */}
      {filteredAffairs.length === 0 && (
        <div className="w-full flex flex-col pt-16 items-center">
          <Image
            src="/assets/no-data.png"
            alt="No Data"
            width={150}
            height={150}
          />
          <div className="text-base font-semibold p-4 text-center">
            {t("Current affairs not available for the selected month")}
            <br />
            {t("Please select another month")}
          </div>
        </div>
      )}
    </MainLayout>
  );
}
