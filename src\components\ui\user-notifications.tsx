import useFcmToken from "@/hooks/useFcmToken";
import { UseNotification } from "@/hooks/useNotification";
import { KEYS } from "@/lib/keys";
import { getLocalStorageItem } from "@/lib/utils";
import { NotificationResponse } from "@/types";
import { BellRing, Link } from "lucide-react";
import { useEffect, useState } from "react";

const NotificationMenu = () => {
  const [open, setOpen] = useState(false);
  const [newNotification, setNewNotification] = useState(false);
  const { messagePayload } = useFcmToken();
  const { getNotifications } = UseNotification();
  const [message, setMessage] = useState<string | null>(null);
  const [allMessages, setAllMesseges] = useState<NotificationResponse[]>([]);
  useEffect(() => {
    if (typeof window !== "undefined") {
      const storedNotificationStatus =
        localStorage.getItem(KEYS.NEW_NOTIFICATION) === "true";
      const storedMessage = localStorage.getItem(KEYS.NOTIFICATION_MESSAGE);
      setNewNotification(storedNotificationStatus);
      if (storedMessage) {
        setMessage(storedMessage);
      }
      fetchNotificationData()
    }
  }, []);

  const fetchNotificationData = async (): Promise<void> => {
    try {
      let userID = getLocalStorageItem(KEYS.USER_ID) || "";
      const orgID = getLocalStorageItem(KEYS.ORG_ID);

      const response = await getNotifications({
        org_id: orgID as string,
        user_id: userID as string,
      });
      setAllMesseges(response);
    } catch (error) {}
  };
  const toggleDropdown = () => {
    setOpen(!open);
    setNewNotification(false);
    localStorage.setItem(KEYS.NEW_NOTIFICATION, "false");
  };

  const truncateMessage = (msg: string) => {
    return msg.length > 50 ? msg.slice(0, 50) + "..." : msg;
  };

  useEffect(() => {
    if (messagePayload) {
      setNewNotification(true);
      localStorage.setItem(KEYS.NEW_NOTIFICATION, "true");
      setMessage(messagePayload);
      localStorage.setItem(KEYS.NOTIFICATION_MESSAGE, messagePayload);
    }
  }, [messagePayload]);

  return (
    <div className="relative">
      <div className="flex justify-end items-center">
        <div className="relative mb-5">
          <BellRing
            className="text-white cursor-pointer"
            onClick={toggleDropdown}
          />
          {newNotification && (
            <span className="absolute top-0 right-0 block h-3 w-3 rounded-full bg-red-500 ring-2 ring-white"></span>
          )}
        </div>
      </div>

      {open && (
        <div className="absolute right-0 mt-2 w-64 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5">
          <ul className="p-2">
            {message ? (
              <li
                className="p-2 hover:bg-gray-200 text-gray-700 text-sm border-b last:border-none"
                title={message}
              >
                {truncateMessage(message) || "No message body available"}
              </li>
            ) : (
              <li className="p-2 text-gray-500">No new notifications</li>
            )}
          </ul>
          <ul className="pl-4  pb-2 text-center cursor-pointer">
            <a
              className="flex text-[var(--color-nav-text)]  hover:text-[var(--color-nav-hover-text)] text-sm text-center underline"
              href="/pages/notifications"
            >
              View All Messages
            </a>
          </ul>
          {/* <div className="p-2">
            <Link href="/all-messages">
              <a className="text-blue-500 hover:underline text-sm">View All Messages</a>
            </Link>
          </div> */}
        </div>
      )}
    </div>
  );
};

export default NotificationMenu;
