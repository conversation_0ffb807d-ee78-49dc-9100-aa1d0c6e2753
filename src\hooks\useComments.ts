import { rpc } from "@/lib/apiConfig";
import { supabase } from "@/lib/client";
import { ORG_KEY } from "@/lib/utils";
import { KEYS } from "@/lib/keys";
import {
  AddCommentRequest,
  AddCommentResponse,
  GetCommentLIkeRequest,
  CheckExamDetailsRequest,
  CheckPointsResponse,
  CommentsResponse,
  LoginUserData,
  GetCommentsRequest,
} from "@/types";

interface UseCommentsReturn {
  getComments: (params: GetCommentsRequest) => Promise<CommentsResponse[]>;
  addComments: (params: AddCommentRequest) => Promise<AddCommentResponse>;
}
const useComments = (): UseCommentsReturn => {
  async function getComments(params:GetCommentsRequest): Promise<CommentsResponse[]> {
    try {

      const { data, error } = await supabase.rpc<string, null>(
        rpc.getComments,
        params
      );
      if (error) {
        throw new Error(error.details);
      }

      if (Array.isArray(data) && data.length > 0) {
        return data as CommentsResponse[];
      }
      return data as CommentsResponse[];
    } catch (error) {
      console.error("Error:", error);
      throw error;
    }
  }

  async function addComments(
    params: AddCommentRequest
  ): Promise<AddCommentResponse> {
    try {
      const { data, error } = await supabase.rpc<string, null>(
        rpc.addComments,
        params
      );
      if (error) {
        throw new Error(error.details);
      }
      return data as AddCommentResponse;
    } catch (error) {
      console.error("Error:", error);
      throw error;
    }
  }
  return {
    getComments,
    addComments,
  };
};
export default useComments;
