"use client";
import React from "react";

export default function ExamModal({
  status,
  closeDialog,
}: {
  status: string;
  closeDialog: () => void;
}): React.JSX.Element {
  const closeModal = () => {
    closeDialog();
  };

  return (
    <div className="fixed left-0 top-0 z-50 flex h-full w-full items-center justify-center">
      <div
        className="fixed left-0 top-0 h-full w-full bg-black bg-opacity-50"
        onClick={closeModal}
      ></div>
      <div className="relative w-full max-w-md rounded-lg bg-white md:w-1/2 lg:w-1/3 p-8">
        <h2 className="text-red-500 font-bold text-center mb-4">Sorry!</h2>
        <p className="text-center mb-8">{status}</p>
        <button
          className="bg-[#9fc089] text-white px-6 py-3 rounded-full w-full"
          onClick={closeModal}
        >
          Choose Another Exam
        </button>
      </div>
    </div>
  );
}
