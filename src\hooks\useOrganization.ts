import { functions, rpc, views } from "@/lib/apiConfig";
import { supabase } from "@/lib/client";
import {
  AssignOrg,
  AssignOrgReturn,
  SentEmailRequest,
  SentEmailResponse,
  UserOrganizationReturn,
} from "@/types";

interface UseOrganizationReturnInterface {
  getUserOrganization: (user_id: string) => Promise<UserOrganizationReturn[]>;
  assignOrganization: (passData: AssignOrg) => Promise<AssignOrgReturn>;
  sentEmail: (params: SentEmailRequest) => Promise<SentEmailResponse>;
}
const getUserOrganization = (): UseOrganizationReturnInterface => {
  async function getUserOrganization(
    user_id: string
  ): Promise<UserOrganizationReturn[]> {
    try {
      const userOrg = views?.userOrgnizationData ?? "";
      const exeQuery = supabase.from(userOrg).select().eq("user_id", user_id);

      const { data, error } = await exeQuery;
      if (error) {
        throw new Error(error.message);
      }

      return data as UserOrganizationReturn[];
      // }
    } catch (error) {
      console.error("Error:", error);
      throw error;
    }
  }
  async function assignOrganization(
    passData: AssignOrg
  ): Promise<AssignOrgReturn> {
    try {
      const requestBody = passData;
      const { data, error } = await supabase.rpc<string, null>(
        rpc.assignOrganization,
        requestBody
      );
      if (error) {
        throw new Error(error.details);
      }
      return data as AssignOrgReturn;
    } catch (error) {
      throw error;
    }
  }
  async function sentEmail(
    params: SentEmailRequest
  ): Promise<SentEmailResponse> {
    try {
      //const formData = new FormData();
      //formData.append("mail_user", params.mail_user);
      //formData.append("mail_subject", params.mail_subject as string);
      //formData.append("mail_content", params.mail_content as string);
      const response = await fetch(functions.sendEmailNotification, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          apikey: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY as string,
          Authorization: "Bearer " + localStorage.getItem("access_token"),
        },
        body: JSON.stringify(params),
      });

      if (response.ok) {
        const data = (await response.json()) as SentEmailResponse;
        return data;
      } else {
        throw new Error("Failed to send notification");
      }
    } catch (error) {
      console.error("Error:", error);
      throw error;
    }
  }
  return {
    getUserOrganization,
    assignOrganization,
    sentEmail,
  };
};

export default getUserOrganization;
