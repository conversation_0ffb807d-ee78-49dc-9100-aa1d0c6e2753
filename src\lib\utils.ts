import { type ClassValue, clsx } from "clsx"
import { twMerge } from "tailwind-merge"
import { KEYS } from "./keys";
import { LiveClassResponse } from "@/types";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}




export const getLocalStorageItem = (key: string): string | null => {
  if (typeof window !== "undefined" && typeof localStorage !== "undefined") {
    return localStorage.getItem(key);
  }
  return null; // Return null if localStorage is not available
};

// Now use the function to export constants
export const USER_DATA = getLocalStorageItem(KEYS.USER_DETAILS);
export const ORG_KEY = getLocalStorageItem(KEYS.ORG_ID);
export const ORG_NAME_KEY = getLocalStorageItem(KEYS.ORG_NAME);
export const CURRENT_AFFAIR_KEY = getLocalStorageItem(KEYS.CURRENT_AFFAIR_CONSTANT);
export const PROFILE_IMAGE_KEY = getLocalStorageItem(KEYS.PROFILE_IMAGE);
export const ACESS_TOKEN_KEY = getLocalStorageItem(KEYS.ACCESS_TOKEN);
export const ORG_DETAILS_KEY = getLocalStorageItem(KEYS.ORG_DETAILS);


/**
 * Helper function to construct meeting URLs from meeting IDs
 * @param meeting The meeting object with platform and ID information
 * @returns The complete URL to join the meeting
 */
export const constructMeetingUrl = (meeting: LiveClassResponse) => {
  const platform = meeting.meeting_type || "zoom";
  const meetingId = meeting.meeting_id || "";
  
  // If URL is directly available, return it
  if (meeting.meeting_url ) {
    return meeting.meeting_url ;
  }
  
  // Otherwise construct URL based on platform and meeting ID
  if (platform === "zoom" && meetingId) {
    const passcode = meeting.passcode ? `?pwd=${meeting.passcode}` : '';
    return `https://zoom.us/j/${meetingId}${passcode}`;
  } 
  else if (platform === "gmeet" && meetingId) {
    // Google Meet links might be in different formats
    if (meetingId.includes('-') && meetingId.length >= 10) {
      // Assuming this is already a Google Meet code (abc-defg-hij format)
      return `https://meet.google.com/${meetingId}`;
    } else {
      // If it's just a simple ID
      return `https://meet.google.com/${meetingId}`;
    }
  }
  else if (platform === "teams" && meetingId) {
    // Microsoft Teams meeting ID format
    return `https://teams.microsoft.com/l/meetup-join/${meetingId}`;
  }
  else if (platform === "webex" && meetingId) {
    // Webex meeting
    return `https://meetingsapac.webex.com/meet/${meetingId}`;
  }
  
  // If we can't determine the URL, return null
  return null;
};

/**
 * Opens the meeting in a new browser tab
 * @param meeting The meeting object
 */
export const joinMeeting = (meeting: LiveClassResponse) => {
  const meetingUrl = constructMeetingUrl(meeting);
  
  if (meetingUrl) {
    window.open(meetingUrl, "_blank");
    return true;
  } else {
    
    console.error("Unable to join meeting: No URL or valid meeting ID available");
    // You could trigger a toast notification here
    return false;
  }
};

/**
 * Checks if the meeting has sufficient information to be joined
 * @param meeting The meeting object
 * @returns Boolean indicating if the meeting can be joined
 */
export const canJoinMeeting = (meeting: LiveClassResponse) => {
  return Boolean(
    (meeting.meeting_url ) || 
    ((meeting.meeting_id ) && 
     (meeting.meeting_type ))
  );
};