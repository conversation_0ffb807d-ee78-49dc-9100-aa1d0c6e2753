"use client";
import { Button } from "@/components/ui/button";
import React from "react";

const CheckPointModal = ({
  closeDialog,
  cancelCheckPoint,
}: {
  closeDialog: () => void;
  cancelCheckPoint: () => void;
}): React.JSX.Element => {
  const closeModal = () => {
    closeDialog();
  };

  return (
    <div className="">
      <p className="text-center mb-4 text-lg font-semibold">
        Check point reached. Now there will be a Quiz to check your
        understanding so far. You will be redirected to the quiz associated with
        the check point.
      </p>
      <p className="text-center mb-6">Do you want to continue?</p>
      <div className="flex justify-center gap-4">
        <Button
          variant="outline"
          className="w-full sm:w-auto"
          onClick={() => {
            cancelCheckPoint();
          }}
        >
          Attend Later
        </Button>
        <Button type="submit" variant= "default" onClick={closeModal}>
          Proceed Now?
        </Button>
      </div>
    </div>
  );
};
export default CheckPointModal;
