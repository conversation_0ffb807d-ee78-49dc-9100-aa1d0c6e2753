"use client";
import { Button } from "@/components/ui/button";
import React from "react";
import { useTranslation } from "next-i18next";

const CheckPointModal = ({
  closeDialog,
  cancelCheckPoint,
}: {
  closeDialog: () => void;
  cancelCheckPoint: () => void;
}): React.JSX.Element => {
  const closeModal = () => {
    closeDialog();
  };
  const { t } = useTranslation("common");
  return (
    <div className="">
      <p className="text-center mb-4 text-lg font-semibold">
        {t("checkpointNotice")}
      </p>
      <p className="text-center mb-6">{t("Do you want to continue?")}</p>
      <div className="flex justify-center gap-4">
        <Button
          variant="outline"
          className="w-full sm:w-auto"
          onClick={() => {
            cancelCheckPoint();
          }}
        >
          {t("Attend Later")}
        </Button>
        <Button type="submit" variant="default" onClick={closeModal}>
          {t("Proceed Now?")}
        </Button>
      </div>
    </div>
  );
};
export default CheckPointModal;
