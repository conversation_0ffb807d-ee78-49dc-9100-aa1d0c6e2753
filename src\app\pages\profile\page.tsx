"use client";
import React, { useEffect, useRef, useState } from "react";
import "../../../styles/main.css";
import MainLayout from "../layouts/mainLayout";
import Image from "next/image";
import { DATE_FORMAT, purchaseData } from "@/lib/constants";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
  CustomBrandingDetails,
  EditProfileFormType,
  ErrorCatch,
  InnerItem,
  LoginDataReturn,
  LoginUserData,
  Plan,
  ProfileFormType,
  subscriptionReportResponse,
} from "@/types";
import { ProfileFormSchema } from "@/schema/schema";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import useSubscriptionReport from "@/hooks/useSubscriptionReport";
import { toast } from "@/components/ui/use-toast";
import { useRouter } from "next/navigation";
import useAuthorization from "@/hooks/useAuth";
import { ERROR_MESSAGES, SUCCESS_MESSAGES } from "@/lib/messages";
import { supabase } from "@/lib/client";
import { getLocalStorageItem, USER_DATA } from "@/lib/utils";
import { KEYS } from "@/lib/keys";
import {
  Book,
  BookOpen,
  Calendar,
  Circle,
  Edit,
  Logs,
  Mail,
  Moon,
  Phone,
  Sun,
} from "lucide-react";
import { CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import moment from "moment";
import RecentActivities from "@/components/dashboard/recentActivities";
import { Label } from "@/components/ui/label";
import NextBreadcrumb from "@/components/breadcrumb";
import getBreadCrumbItems from "@/hooks/useBreadcrumbs";
import EditProfile from "@/components/edit-profile";
import { Modal } from "@/components/ui/modal";
import { DropdownMenu } from "@/components/ui/dropdown-menu";
import { useTheme } from "@/context/ThemeContext";

export default function Profile(): React.JSX.Element {
  const router = useRouter();
  const [editValue, setEditValue] = useState<boolean>(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [openIndex, setOpenIndex] = useState<string | undefined>(undefined); // Use undefined instead of null
  const [imageUrl, setImageUrl] = useState<string>();
  const [usersInfo, setUserInfo] = useState<LoginDataReturn>();
  const { subscriptionReport } = useSubscriptionReport();
  const [subscriptionReportData, setsubscriptionReportData] =
    useState<subscriptionReportResponse | null>(null);
  const [subscriptionReportPendingData, setsubscriptionReportPendingData] =
    useState<Plan[]>([]);
  const [subscriptionReportApprovedData, setsubscriptionReportApprovedData] =
    useState<Plan[]>([]);
  const [fileURL, setfileURL] = useState<string>();
  const [showButton, setShowButton] = useState(false);
  const [selectedImage, setSelectedImage] = useState<File | null>(null);
  const [userId, setUserId] = useState<string>("");
  const { getProfileImage } = useAuthorization();
  const { theme, setTheme } = useTheme();
  const [firstName, setFirstName] = useState("");
  const [lastName, setLastName] = useState("");
  const [phoneNumber1, setPhoneNumber1] = useState("");
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [themeList, setThemeList] = useState<string[]>([]);
  const handleToggle = (value: string) => {
    if (openIndex === value) {
      setOpenIndex(undefined);
    } else {
      setOpenIndex(value);
    }
  };
  const [breadcrumbItems, setBreadcrumbItems] = useState<InnerItem[]>([]);
  const handleFileChange = async (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    const file = event.target.files?.[0];
    if (
      file &&
      (file.type === "image/png" ||
        file.type === "image/jpeg" ||
        file.type === "image/jpg")
    ) {
      if (file?.size <= 1048576) {
        setSelectedImage(file);
        const reader = new FileReader();
        reader.onloadend = () => {
          setImageUrl(reader.result as string);
        };
        reader.readAsDataURL(file);
        const fileExt = file.name.split(".").pop();
        const fileName = `${Math.random()}.${fileExt}`;
        const filePath = `${fileName}`;
        const orgID = getLocalStorageItem(KEYS.ORG_ID);
        if (USER_DATA) {
          const userInfo = JSON.parse(USER_DATA) as LoginUserData;
          const userId = userInfo.id;
          setUserId(userId);
          try {
            const result = await supabase.storage
              .from(`${orgID}`)
              .upload(`avatars/${userId}/${fileName}`, file, {
                cacheControl: "3600",
                upsert: true,
              });
          } catch (error) {
            console.error("Error uploading avatar: ", error);
          }
          const { data } = supabase.storage
            .from(`${orgID}`)
            .getPublicUrl(`avatars/${userId}/${fileName}`);
          setfileURL(data.publicUrl);
        } else {
          toast({
            variant: "destructive",
            title: ERROR_MESSAGES.error,
            description: "Please select a valid image file (PNG or JPG)",
          });
        }
      } else {
        toast({
          variant: "destructive",
          title: ERROR_MESSAGES.error,
          description: ERROR_MESSAGES.file_size_limit,
        });
      }
    }
  };

  const form = useForm<ProfileFormType>({
    resolver: zodResolver(ProfileFormSchema),
  });
  
  useEffect(() => {
    const branding = localStorage.getItem("brandingDetails");
    if (branding) {
      const brandingJson = JSON.parse(
        localStorage.getItem("brandingDetails") ?? ""
      );

      const themeNames = brandingJson.map(
        (item: CustomBrandingDetails) => item.theme_name
      );
      setThemeList(themeNames);
      if (branding) setShowButton(true);
    }
  }, []);

  useEffect(() => {
    const firstName = localStorage?.getItem("PROFILE_FNAME") as string;
    const lastName = localStorage?.getItem("PROFILE_LNAME") as string;
    const phoneNumber = localStorage?.getItem("PROFILE_PHONE_NUMBER") as string;
    setFirstName(firstName);
    setLastName(lastName);
    setPhoneNumber1(phoneNumber);
    const org_id = localStorage.getItem(KEYS.ORG_ID);
    const userDetails = localStorage.getItem("userDetails");
    console.log("userDetails", userDetails);

    if (userDetails !== null && userDetails !== undefined) {
      const userInfo = JSON.parse(userDetails);
      setUserInfo(userInfo);
    }
    getProfileImageFromLs();
    const fetchSessionData = async (): Promise<void> => {
      try {
        const resultData = await subscriptionReport(org_id as string);
        setsubscriptionReportData(resultData);
        const pendingPlans =
          resultData?.result.pending_plan_lists_by_user?.filter(
            (plan) => !plan.is_expired
          );
        const purchasedPlans =
          resultData?.result.purchased_plan_lists_by_user.filter(
            (plan) => !plan.is_expired
          );
        setsubscriptionReportPendingData(pendingPlans as Plan[]);
        setsubscriptionReportApprovedData(purchasedPlans as Plan[]);
      } catch (error) {
        console.log(error);
      }
    };
    fetchSessionData()
      .then((response) => {
        console.log(response);
      })
      .catch((error) => {
        console.log(error);
      });

    setBreadcrumbItems(getBreadCrumbItems("Profile", {}));
  }, [isModalOpen]);

  const getProfileImageFromLs = (): void => {
    const profileImage = localStorage.getItem("profile_image");
    if (profileImage !== null) {
      const parsedData = JSON.parse(profileImage) as {
        avatar_url?: string;
      }[];
      const image_url = parsedData[0]?.avatar_url;
      if (image_url != null) {
        setImageUrl(image_url);
      } else {
        setImageUrl("/images/profile.png");
      }
    } else {
      setImageUrl("/images/profile.png");
    }
  };
  const handleDialogClose = (): void => {
    setIsModalOpen(false);
  };

  return (
    <MainLayout titleText="">
      <NextBreadcrumb
        items={breadcrumbItems}
        separator={<span> | </span>}
        containerClasses="flex py-5"
        listClasses="hover:underline mx-2 font-bold"
        capitalizeLinks
      />
      <div className="container mx-auto px-4 py-6">
        <div className="w-full flex justify-end mt-6 mb-2">
          {showButton && (
            <div className="p-1 rounded-xl shadow-xl border border-black">
              {themeList && themeList.length > 0 && (
                <button
                  className="flex items-center gap-2 px-6 py-3 bg-white font-semibold rounded-lg hover:bg-gray-100 transition duration-300 ease-in-out"
                  onClick={() => {
                    // Find the next theme in the list, cycling through available themes only
                    if (themeList.length === 0) return;
                    const currentIndex = themeList.indexOf(theme);
                    const nextIndex =
                      currentIndex === -1 ||
                      currentIndex === themeList.length - 1
                        ? 0
                        : currentIndex + 1;
                    const newTheme = themeList[nextIndex];
                    setTheme(newTheme);
                    localStorage.setItem("theme", newTheme);
                  }}
                >
                  {theme === "light" ? (
                    <Moon className="w-5 h-5" />
                  ) : theme === "dark" ? (
                    <Sun className="w-5 h-5" />
                  ) : (
                    <Circle className="w-5 h-5" />
                  )}
                  <span>
                    {themeList.includes(theme)
                      ? `Current: ${
                          theme.charAt(0).toUpperCase() + theme.slice(1)
                        }`
                      : "Switch Theme"}
                  </span>
                </button>
              )}
            </div>
          )}
        </div>

        {!editValue && (
          <Card className="w-full  border-none shadow-lg">
            <CardContent className="pt-6">
              <div className="flex flex-col md:flex-row items-center md:items-start gap-6">
                <div className="relative">
                  <Avatar className="w-24 h-24 md:w-32 md:h-32 border-4 border-white shadow-lg">
                    <AvatarImage src={imageUrl} alt="Profile picture" />
                    {/* <AvatarFallback className=" text-white text-2xl">
                      {usersInfo?.user_metadata.first_name ?? firstName ?? ""}{" "}
                      {usersInfo?.user_metadata.last_name ?? lastName}
                    </AvatarFallback> */}
                  </Avatar>
                </div>

                <div className="flex-1 space-y-4 text-center md:text-left">
                  <div className="flex flex-col md:flex-row justify-between items-center md:items-start space-y-4 md:space-y-0">
                    <div className="space-y-2">
                      <h2 className="text-2xl md:text-3xl font-bold text-[var(--color-font-color)]">
                        {usersInfo?.user_metadata.first_name ?? firstName ?? ""}{" "}
                        {usersInfo?.user_metadata.last_name ?? lastName}
                      </h2>
                      <div className="flex flex-col space-y-2 text-gray-600">
                        <div className="flex items-center justify-center md:justify-start gap-2">
                          <Mail className="w-4 h-4 text-primary" />
                          <span className="text-[var(--color-font-color)]">
                            {" "}
                            {usersInfo?.user_metadata.email}
                          </span>
                        </div>
                        <div className="flex items-center justify-center md:justify-start gap-2">
                          <Phone className="w-4 h-4 text-primary" />
                          <span className="text-[var(--color-font-color)]">
                            {" "}
                            {usersInfo?.user_metadata.phonenumber1 ??
                              phoneNumber1 ??
                              " "}
                          </span>
                        </div>
                      </div>
                    </div>

                    {!editValue && (
                      <Button
                        className=" mt-4 md:mt-0"
                        onClick={() => {
                          //   setEditValue(true);
                          //   form.setValue(
                          //     "firstName",
                          //     usersInfo?.user_metadata.first_name ?? firstName ?? ""
                          //   );
                          //   form.setValue(
                          //     "lastName",
                          //     usersInfo?.user_metadata.last_name ?? lastName ?? ""
                          //   );
                          //   form.setValue("email", usersInfo?.email ?? "");
                          setIsModalOpen(true);
                        }}
                      >
                        <Edit />
                      </Button>
                    )}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        )}
        {/* {editValue && (
          <div className="flex flex-col items-center">
            <div className="relative">
              <img
                src={imageUrl}
                alt="Avatar"
                className="w-40 h-40 object-cover border-2 border-white rounded-full"
              />
              <div className="absolute z-10 left-camera  border rounded-full stroke-white bg-[#FDB666] border-white">
                <Label htmlFor="file-input" style={{ cursor: "pointer" }}>
                  <Image
                    src="/images/profile_camera.png"
                    alt="Image"
                    width={50}
                    height={50}
                  />
                </Label>
                <Input
                  id="file-input"
                  type="file"
                  accept="image/png, image/jpeg,image/jpg"
                  style={{ display: "none" }}
                  onChange={handleFileChange}
                />
              </div>
            </div>
          </div>
        )} */}

        {!editValue && (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mt-6">
            <Card className="bg-white shadow-md">
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-[var(--color-font-color)]">
                  <BookOpen className="w-5 h-5 text-primary " />
                  Requested Courses
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {subscriptionReportPendingData &&
                  subscriptionReportPendingData.length > 0 ? (
                    subscriptionReportPendingData.map((course) => (
                      <div
                        key={course.plan_name}
                        className="p-4 border rounded-lg hover:border-purple-200 transition-colors"
                      >
                        <div className="flex items-center justify-between mb-3">
                          <h3 className="font-semibold text-lg text-purple-900 break-words w-4/5">
                            {course.plan_name}
                          </h3>
                          <Badge className="text-center text-sm bg-yellow-400 text-black border">
                            {"Pending"}
                          </Badge>
                        </div>

                        <div className="space-y-2 text-sm text-gray-600">
                          <div className="flex items-center gap-2">
                            <Calendar className="w-4 h-4" />
                            Starts:{" "}
                            {moment
                              .utc(course.subscription_valid_from)
                              .local()
                              .format(DATE_FORMAT)}
                          </div>
                          <div className="flex items-center gap-2">
                            <Calendar className="w-4 h-4" />
                            Ends:{" "}
                            {moment
                              .utc(course.subscription_valid_to)
                              .local()
                              .format(DATE_FORMAT)}
                          </div>
                        </div>
                      </div>
                    ))
                  ) : (
                    <p className="text-[var(--color-font-color)] text-center">
                      No requested courses found
                    </p>
                  )}
                </div>
              </CardContent>
            </Card>

            <Card className="bg-white shadow-md">
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-[var(--color-font-color)]">
                  <Book className="w-5 h-5 text-primary" />
                  Purchased Courses
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {subscriptionReportApprovedData &&
                  subscriptionReportApprovedData.length > 0 ? (
                    subscriptionReportApprovedData.map((course) => (
                      <div
                        key={course.plan_name}
                        className="p-4 border rounded-lg hover:border-purple-200 transition-colors"
                      >
                        <div className="flex items-center justify-between mb-3">
                          <h3 className="font-semibold text-lg text-purple-900 break-words w-4/5">
                            {course.plan_name}
                          </h3>
                        </div>

                        <div className="space-y-2 text-sm text-gray-600">
                          <div className="flex items-center gap-2">
                            <Calendar className="w-4 h-4" />
                            Starts:{" "}
                            {moment
                              .utc(course.subscription_valid_from)
                              .local()
                              .format(DATE_FORMAT)}
                          </div>
                          <div className="flex items-center gap-2">
                            <Calendar className="w-4 h-4" />
                            Ends:{" "}
                            {moment
                              .utc(course.subscription_valid_to)
                              .local()
                              .format(DATE_FORMAT)}
                          </div>
                        </div>
                      </div>
                    ))
                  ) : (
                    <p className="text-[var(--color-font-color)] text-center">
                      No Purchased Courses found
                    </p>
                  )}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-[var(--color-font-color)]">
                  <Logs className="text-primary" />
                  Recent Activities
                </CardTitle>
              </CardHeader>
              <CardContent>
                <RecentActivities />
              </CardContent>
            </Card>
          </div>
        )}
      </div>
      {isModalOpen && (
        <Modal
          title="Update Profile"
          header=""
          openDialog={isModalOpen}
          closeDialog={handleDialogClose}
          type="max-w-lg"
        >
          <EditProfile closeDialog={handleDialogClose} isDashboard={false} />
        </Modal>
      )}
    </MainLayout>
  );
}
