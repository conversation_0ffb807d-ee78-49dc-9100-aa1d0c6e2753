"use client";
import React, { useEffect, useState } from "react";
import { Card } from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { DataTable } from "primereact/datatable";
import { Column } from "primereact/column";
import {
  GraduationCap,
  ClipboardList,
  Clock,
  CheckCircle2,
  XCircle,
  ShieldCheck,
  Activity,
} from "lucide-react";
import MainLayout from "../layouts/mainLayout";
import { getLocalStorageItem } from "@/lib/utils";
import {
  AttemptedQuizRequest,
  AttemptedQuizResponse,
  CourseDetailsResultType,
  ErrorCatch,
  InnerItem,
  LoginUserData,
} from "@/types";
import { KEYS } from "@/lib/keys";
import { useExam } from "@/hooks/useExam";
import "primereact/resources/themes/lara-light-indigo/theme.css";
import NextBreadcrumb from "@/components/breadcrumb";
import getBreadCrumbItems from "@/hooks/useBreadcrumbs";
const EnhancedQuizAttempts = () => {
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [attemptedQuizData, setAttemptedQuizData] = useState<
    AttemptedQuizResponse[]
  >([]);
  const { examResultAnalysis } = useExam();
  const [courseData, setCourseDetails] = useState<CourseDetailsResultType[]>(
    []
  );
  const [selectedCourse, setSelectedCourse] = useState<string | null>(null);
  const [breadcrumbItems, setBreadcrumbItems] = useState<InnerItem[]>([]);
  useEffect(() => {
    const courseDatas = localStorage.getItem("courseData");
    let data = JSON.parse(courseDatas as string);
    if (courseDatas) {
      setCourseDetails(data);
    }
    if (data.length === 1) {
      getAttemptedQuizesList(data[0].course_id);
      setSelectedCourse(data[0].course_id);
    }
    const courseID = localStorage.getItem(KEYS.COURSE_ID);
    if (courseID) {
      setSelectedCourse(courseID);
      getAttemptedQuizesList(courseID);
    }
    setBreadcrumbItems(
      getBreadCrumbItems("Exam History", {})
    );
  }, []);

  const getAttemptedQuizesList = async (courseID: string): Promise<void> => {
    const orgID = localStorage.getItem(KEYS.ORG_ID);
    const USER_DATA = getLocalStorageItem("userDetails");

    if (USER_DATA) {
      const userInfo = JSON.parse(USER_DATA) as LoginUserData;
      const params: AttemptedQuizRequest = {
        course_id: courseID as string,
        org_id: orgID as string,
        user_id: userInfo.id,
        type_of_quiz: "Practice",
      };

      try {
        const quizesList = await examResultAnalysis(params);
        setAttemptedQuizData(quizesList);
        setIsLoading(false);
      } catch (error) {
        const err = error as ErrorCatch;
        setIsLoading(false);
        console.error("Error fetching quiz attempts:", err);
      }
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  const handleSelectionChange = (value: string) => {
    getAttemptedQuizesList(value);
    setSelectedCourse(value);
  };

  const StatusBadge: React.FC<{
    status: string | null;
    passMark: number;
    sumGrades: number;
  }> = ({ status, passMark, sumGrades }) => {
    const getStatusStyle = () => {
      if (status === null)
        return "bg-gray-100 text-gray-600 border border-gray-300";
      return sumGrades >= passMark
        ? "bg-green-50 text-green-700 border border-green-300"
        : "bg-red-50 text-red-700 border border-red-300";
    };

    const getStatusIcon = () => {
      if (status === null) return <Clock className="w-4 h-4 mr-2" />;
      return sumGrades >= passMark ? (
        <CheckCircle2 className="w-4 h-4 mr-2" />
      ) : (
        <XCircle className="w-4 h-4 mr-2" />
      );
    };

    return (
      <div
        className={`
        flex items-center justify-center 
        px-3 py-1 rounded-full text-sm font-medium
        ${getStatusStyle()}
      `}
      >
        {getStatusIcon()}
        {status === null
          ? "In Progress"
          : sumGrades >= passMark
          ? "Passed"
          : "Failed"}
      </div>
    );
  };

  return (
    <MainLayout titleText="">
       <NextBreadcrumb
        items={breadcrumbItems}
        separator={<span> | </span>}
        containerClasses="flex py-5"
        listClasses="hover:underline mx-2 font-bold"
        capitalizeLinks
      />
      <div className="px-4 py-6">
        <div className="mb-6">
          <Select
            value={selectedCourse || ""}
            onValueChange={handleSelectionChange}
          >
            <SelectTrigger className="w-full max-w-md mx-auto">
              <SelectValue placeholder="Select a Course">
                {selectedCourse
                  ? courseData.find((c) => c.course_id === selectedCourse)
                      ?.full_name
                  : "Select a Course"}
              </SelectValue>
            </SelectTrigger>
            <SelectContent>
              {courseData.map((course) => (
                <SelectItem
                  key={course.course_id}
                  value={course.course_id}
                  className="hover:bg-gray-100"
                >
                  {course.full_name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {attemptedQuizData?.length === 0 || attemptedQuizData === null ? (
          <div className="text-center py-12 bg-gray-50 rounded-lg">
            <GraduationCap className="mx-auto h-16 w-16 text-gray-400 mb-4" />
            <p className="text-xl text-gray-600">No Exam History Found</p>
          </div>
        ) : (
          <div className="space-y-6">
            {attemptedQuizData?.map((exam, examIndex) => (
              <Card
                key={examIndex}
                className="border border-gray-200 rounded-xl shadow-sm hover:shadow-md transition-shadow "
              >
                <div className="p-6">
                  <div className="flex items-center mb-4">
                    <GraduationCap className="h-8 w-8 text-primary  mr-3" />
                    <h2 className="text-xl font-semibold text-[var(--color-font-color)]">
                      {exam.name}
                    </h2>
                  </div>

                  <div className="grid grid-cols-2 md:grid-cols-3 gap-4 mb-6 text-sm text-gray-600">
                    <div className="flex items-center">
                      <ClipboardList className="w-4 h-4 mr-2 text-primary" />
                      <span className="text-[var(--color-font-color)]">Total Mark: {exam.total_mark}</span>
                    </div>
                    <div className="flex items-center">
                      <CheckCircle2 className="w-4 h-4 mr-2 text-primary" />
                      <span className="text-[var(--color-font-color)]">Pass Mark: {exam.pass_mark}</span>
                    </div>
                    <div className="flex items-center ">
                      <Clock className="w-4 h-4 mr-2 text-primary" />
                      <span className="text-[var(--color-font-color)]">Duration: {exam.duration}m</span>
                    </div>
                    <div className="flex items-center ">
                      <ShieldCheck className="w-4 h-4 mr-2 text-primary" />
                      <span className="text-[var(--color-font-color)]">Allowed Attempts: {exam.allowed_attempts}</span>
                    </div>
                    <div className="flex items-center">
                      <Activity className="w-4 h-4 mr-2 text-primary" />
                      <span className="text-[var(--color-font-color)]">Remaining Attempts: {exam.attempts_remaining}</span>
                    </div>
                  </div>

                  <DataTable
                    value={exam.attempt_ids_of_quiz || []}
                    dataKey="attempt_id"
                    tableClassName="w-full"
                    className="p-datatable-striped "
                    stripedRows
                    paginator
                    rows={5}
                    rowsPerPageOptions={[5, 10, 25]}
                    paginatorTemplate="FirstPageLink PrevPageLink PageLinks NextPageLink LastPageLink CurrentPageReport RowsPerPageDropdown"
                    currentPageReportTemplate="Showing {first} to {last} of {totalRecords} attempts"
                  >
                    <Column field="attempt" header="Attempt" sortable />
                    <Column field="sum_grades" header="Mark" sortable />
                    <Column
                      header="Status"
                      body={(rowData) => (
                        <StatusBadge
                          status={rowData.end_time}
                          passMark={exam.pass_mark}
                          sumGrades={rowData.sum_grades}
                        />
                      )}
                      sortable
                    />
                    <Column
                      field="date"
                      header="Started On"
                      body={(rowData) => formatDate(rowData.date)}
                      sortable
                    />
                    <Column
                      field="end_time"
                      header="Submitted On"
                      body={(rowData) =>
                        rowData.end_time ? formatDate(rowData.end_time) : "—"
                      }
                      sortable
                    />
                  </DataTable>
                </div>
              </Card>
            ))}
          </div>
        )}
      </div>
    </MainLayout>
  );
};

export default EnhancedQuizAttempts;
