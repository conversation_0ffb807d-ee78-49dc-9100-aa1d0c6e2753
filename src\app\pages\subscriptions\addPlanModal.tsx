"use client";
import { But<PERSON> } from "@/components/ui/button";
import { useToast } from "@/components/ui/use-toast";
import { useSubscription } from "@/hooks/useSubscription";
import { USER_DATA } from "@/lib/utils";
import { KEYS } from "@/lib/keys";
import { SUCCESS_MESSAGES } from "@/lib/messages";
import { AddSubscriptionRequest, LoginUserData, ToastType } from "@/types";

import { useRouter } from "next/navigation";
import React from "react";
import { UseLogClass } from "@/hooks/useLog";

export default function AddPlanConfirmation({
 id,
  closeDialog,
}: {
  id: string;
  status: string;
  closeDialog: () => void;
}): React.JSX.Element {
  const { toast } = useToast() as ToastType;
  const closeModal = () => {
    closeDialog();
  };
  const { insertLogDetails } = UseLogClass();
  const router = useRouter();
  const { addSubscriptionPlan } = useSubscription();
  const purchasePlan = () => {
    getSubscriptionDetails();
    closeDialog();
  };
  const getSubscriptionDetails = async () => {
    try {
      const orgID = localStorage.getItem(KEYS.ORG_ID);
      if (USER_DATA && orgID) {
        const userInfo = JSON.parse(USER_DATA) as LoginUserData;
        const userId = userInfo.id;
        {
          let passData: AddSubscriptionRequest = {
            org_id: orgID,
            subscription_plan_for_user_data: { payment_method: "cash" },
            plan_id: id,
            user_id: userId,
          };
          const res = await addSubscriptionPlan(passData);
          if(res.status === 'success'){
            toast({
              variant: "default",
              title: SUCCESS_MESSAGES.title,
              description: SUCCESS_MESSAGES.addPlanResponse,
            });
            await insertLogDetails(
              "Subscription",
              "Subscription",
              `Subscription plan requested `,
              "SUCCESS",
              id
            );

            router.push(`/pages/profile`);
          }
        }
      }
    } catch (error) {
      console.error("Error fetching subscription details:", error);
      await insertLogDetails(
        "Subscription",
        "Subscription",
        `Failed to request subscription plan  `,
        "ERROR",
        id
      );
    }
  };
  
  return (
    <div className="fixed left-0 top-0 z-50 flex h-full w-full items-center justify-center">
    <div
      className="fixed left-0 top-0 h-full w-full bg-black bg-opacity-50"
      onClick={closeModal}
    ></div>
    <div className="relative w-full max-w-lg rounded-lg bg-white md:w-1/2 lg:w-1/3 p-8 border border-gray-300">
      <h2 className="text-teal-500 font-bold text-center mb-4 text-2xl">
        Confirm Subscription
      </h2>
      <p className="text-center text-xl mb-8">
        Would you like to subscribe to the selected plan?
      </p>
      <div className="flex justify-end mt-8">
        <Button
          type="button"
          variant="outline"
          className="primary border border-gray-300 px-6 py-2 rounded-lg text-black"
          onClick={closeModal}
        >
          No
        </Button>
        &nbsp; &nbsp;
        <Button
          type="submit"
          className=" px-6 py-2 rounded-lg"
          variant="default"
          onClick={purchasePlan}
        >
          Yes
        </Button>
      </div>
    </div>
  </div>
  
  );
}
