"use client";

import React, { useEffect, useState } from "react";
import { getLocalStorageItem } from "@/lib/utils";
import { useCourse } from "@/hooks/useCourse";
import { KEYS } from "@/lib/keys";
import { useTranslation } from "next-i18next";
import {
  CategoryWiseProgressType,
  CourseStats,
  SubjectMarksType,
  SubjectProgressType,
  UserStatistics,
} from "@/types";

import SubjectsProgress from "@/components/dashboard/subjectsProgress";
import MarksCardGraph from "@/components/dashboard/marksCard";
import "../../../styles/main.css";
import CompletionCardGraph from "@/components/dashboard/completionCard";
import SkillAnalysis from "@/components/dashboard/skillAnalysis";
import CourseDetailsModal from "./courseDetailsModal";
import { Modal } from "@/components/ui/modal";

interface CourseStatistics {
  totalMarksGot: number;
  percCompleted: number;
  hoursCompleted: string;
  achievements: number;
}
interface CourseStatisticsProps {
  courseID: string;
}

const DashboardAnalytics: React.FC<CourseStatisticsProps> = ({ courseID }) => {
  const { t } = useTranslation("common");
  const { getAllCourseDatas, getCategorySummaryAllCourse } = useCourse();
  const orgID = getLocalStorageItem(KEYS.ORG_ID);
  const [isLoading, setIsLoading] = useState(false);
  const [courseStatisticsAll, setCourseStaticsAll] = React.useState<
    UserStatistics[]
  >([]);
  const [userStatistics, setUserStatics] = React.useState<UserStatistics[]>([]);
  const [dashboardStats, setDashboardStats] =
    React.useState<CourseStatistics>();
  const [courseListWithCounter, setCourseListWithCounter] = React.useState<
    CourseStats[]
  >([]);
  const [openModal, setIsOpenModal] = React.useState<boolean>(false);
  const [courseLength, setCourseLength] = React.useState<number>(0);
  const [courseProgress, setCourseProgress] = React.useState<
    SubjectProgressType[]
  >([]);
  const [courseMarks, setCourseMarks] = React.useState<SubjectMarksType[]>([]);
  const [skillData, setSkillData] = useState<CategoryWiseProgressType[]>([]);
  const [modalTitle, setModalTitle] = useState<string>("");

  useEffect(() => {
    getAllCourseStatisticsDetails();
    getPerformanceDetailsAll();
  }, []);

  // Function to convert HH:MM:SS to seconds
  const timeToSeconds = (time: string) => {
    const [hours, minutes, seconds] = time.split(":").map(Number);
    return hours * 3600 + minutes * 60 + seconds;
  };

  const getAllCourseStatisticsDetails = async (): Promise<void> => {
    try {
      let user_id = getLocalStorageItem(KEYS.USER_ID) || "";
      const response = await getAllCourseDatas({
        org_id: orgID as string,
        user_id: user_id as string,
      });

      setCourseStaticsAll(response);
      if (response) {
        const subjectPerformance = response.map((item) => {
          const progress = Math.min(item.progress, 100); // Ensure progress doesn't exceed 100
          return {
            subject: item.course_name,
            completed: progress,
            ongoing: 100 - progress,
          };
        });

        setCourseProgress(subjectPerformance);
        const subjectMarks = response.map((item) => ({
          subject: item.course_name,
          marks: item.totalMarks,
        }));
        setCourseMarks(subjectMarks);
      }

      setIsLoading(true);

      setCourseLength(response?.length);
      const firstFiveItems = response.slice(0, 5);
      const allItems = response;

      setUserStatics(firstFiveItems);

      // calculate cumulative percentage of time
      const totalProgress = allItems.reduce(
        (sum, progress) => sum + progress.progress,
        0
      );
      const cumulativePercentage = totalProgress / allItems.length;

      // Calculate the total time spent
      const totalTimeInSeconds = allItems.reduce(
        (sum, progress) => sum + timeToSeconds(String(progress.time_spent)),
        0
      ); //firstFiveItems.map(timeToSeconds).reduce((sum, seconds) => sum + seconds, 0);

      // Convert back to HH:MM:SS format
      const totalHoursCovered = totalTimeInSeconds;

      const totalMarks =
        allItems.reduce(
          (accumulator, currentValue) =>
            accumulator + Number(currentValue.totalMarks),
          0
        ) || 0;
      const totalPercentCovered =
        allItems.reduce(
          (accumulator, currentValue) => cumulativePercentage,
          0
        ) || 0;

      const totalAchievements =
        allItems.reduce(
          (accumulator, currentValue) =>
            accumulator + Number(currentValue.achievements),
          0
        ) || 0;
      const overAllStatus = {
        totalMarksGot: totalMarks,
        percCompleted: totalPercentCovered,
        hoursCompleted: String(totalHoursCovered),
        achievements: totalAchievements,
      };

      setDashboardStats(overAllStatus as CourseStatistics);
    } catch (error) {
      setIsLoading(true);
    }
  };

  const getPerformanceDetailsAll = async (): Promise<void> => {
    const orgID = getLocalStorageItem(KEYS.ORG_ID);

    try {
      let user_id = getLocalStorageItem(KEYS.USER_ID) || "";
      const response = await getCategorySummaryAllCourse({
        org_id: orgID as string,
        user_id: user_id as string,
      });
      setIsLoading(false);
      const newSkillsData = response.performance_summary?.question_category
        ?.map((category) => ({
          name: category.category_name,
          value: category.marks_obtained || 0,
        }))
        .filter((skill) => skill.value > 0);
      console.log("newSkillsData", newSkillsData);

      setSkillData(newSkillsData);
    } catch (error) {
      setIsLoading(false);
    }
  };
  const getModalData = async (
    data: any,
    type: string,
    value: string,
    valuetype: string,
    title?: string
  ): Promise<void> => {
    setModalTitle(title ? title : valuetype);
    setIsOpenModal(true);
    const statsData = data?.map((stats: any) => ({
      course_name: stats[type],
      valuetype: valuetype,
      value: stats[value],
    }));
    setCourseListWithCounter(statsData as CourseStats[]);
  };
  return (
    <div className="">
      <div className="flex flex-wrap md:flex-nowrap gap-4">
        <h2 className="text-2xl  font-bold mb-2"> {t("Analytics")}</h2>
      </div>

      <main className="flex flex-col">
        <div
          dir="ltr"
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 nexthub-grid-cols-2 gap-4 text-[var(--color-font-color)]"
        >
          <div
            onClick={() =>
              getModalData(
                courseProgress,
                "subject",
                "completed",
                "percentage",
                "Completion"
              )
            }
          >
            <CompletionCardGraph SubjectProgress={courseProgress} />
          </div>
          <div
            onClick={() =>
              getModalData(skillData, "name", "value", "Value", "Skills")
            }
          >
            <SkillAnalysis categorySummary={skillData} />
            {/* {skillData?.length > 0 && (
              <SkillAnalysis categorySummary={skillData} />
            )} */}
          </div>
          <div
            onClick={() =>
              getModalData(courseMarks, "subject", "marks", "Marks")
            }
          >
            {/* <MarksCardGraph MarkDetails={courseMarks} /> */}
            {courseMarks?.length > 0 && (
              <MarksCardGraph MarkDetails={courseMarks} />
            )}
          </div>
          <div
            onClick={() =>
              getModalData(courseProgress, "subject", "completed", "Progress")
            }
          >
            <SubjectsProgress
              SubjectProgress={courseProgress}
            ></SubjectsProgress>
          </div>
        </div>
        {openModal && (
          <Modal
            title=""
            header=""
            openDialog={openModal}
            closeDialog={() => {
              setIsOpenModal(false);
            }}
            type="max-w-3xl"
          >
            <CourseDetailsModal
              closeDialog={() => {
                setIsOpenModal(false);
              }}
              CourseStatistics={courseListWithCounter}
              CourseId={courseID}
              isMain={true}
              title={modalTitle}
            />
          </Modal>
        )}
      </main>
    </div>
  );
};

export default DashboardAnalytics;
