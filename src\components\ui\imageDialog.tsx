"use client";
import { Button } from "@/components/ui/button";
import { KEYS } from "@/lib/keys";
import { getLocalStorageItem } from "@/lib/utils";
import Image from "next/image";
export default function ImageDialog({
  url,
  onCancel,
  showTopic
}:
{
  onCancel: () => void;
  url?: string;
  showTopic?: boolean;
  }): JSX.Element {

  const getResourceData = getLocalStorageItem(KEYS.RESOURCE_DATA);
  const parsedData = getResourceData ? JSON.parse(getResourceData) : null;
  const myLoader = (): string => {
    return url as string;
  };
  return (
    <div className="flex flex-col items-center">
      <div className="w-full  mb-2">
      {showTopic && (
          <>
            <p className="text-lg font-semibold mt-2 mb-2">
              Topic: {parsedData.topic_name}
            </p>
            <p className="text-lg font-semibold mt-2 mb-2">
              Name: {parsedData.name}
            </p>
          </>
        )}
      </div>
      <div className="relative w-full rounded-md overflow-hidden border border-gray-300">
        <div className="rounded-md overflow-hidden">
          <Image
            loader={myLoader}
            src={url as string}
            alt="image__preview"
            width={800}
            height={500}
            layout="responsive"
          />
        </div>
      </div>

      <div className="w-full flex justify-end mt-4 ">
        <Button className="bg-[#9fc089]" onClick={onCancel}>
          Close
        </Button>
      </div>
    </div>
  );
}
