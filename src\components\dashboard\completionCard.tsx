import React, { useEffect, useState } from "react";
import { Spin<PERSON> } from "../ui/progressiveLoder";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ontent, Card } from "../ui/card";
import { CategoryWiseProgressType, SubjectProgressType } from "@/types";
// import ReactApexChart from "react-apexcharts";
import { ResponsiveContainer } from "recharts";
import dynamic from "next/dynamic";
import { useTranslation } from "next-i18next";
interface SubjectProgressInterface {
  SubjectProgress: SubjectProgressType[];
}
// Dynamically import ReactApexChart to avoid SSR issues
const ReactApexChart = dynamic(() => import("react-apexcharts"), {
  ssr: false,
});
const CompletionCardGraph = ({ SubjectProgress }: SubjectProgressInterface) => {
  const { t } = useTranslation("common");
  const [skillData, setSkillData] = useState<CategoryWiseProgressType[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [hasData, setHasData] = useState(true);
  const [totalCompletion, setTotalCompletion] = useState(0);

  useEffect(() => {
    const newSkillsData = SubjectProgress?.map((item: any) => ({
      name: item.subject,
      value: item.completed,
    }));

    setSkillData(newSkillsData.slice(0, 5)); // Display only first 5 items

    const total = newSkillsData.reduce((acc, curr) => acc + curr.value, 0);
    const average = (total / (newSkillsData.length || 1)).toFixed(2);
    setTotalCompletion(parseFloat(average));

    const validDataExists = newSkillsData.some((item) => item.value > 0);
    setHasData(validDataExists);

    setIsLoading(false);
  }, [SubjectProgress]);

  const chartOptions = {
    chart: {
      height: 350,
      type: "radialBar" as "radialBar",
    },
    plotOptions: {
      radialBar: {
        hollow: {
          size: "70%",
        },
        dataLabels: {
          show: true,
          value: {
            fontSize: "24px",
            fontWeight: "bold",
            color: "#008FFB",
          },
        },
      },
    },
    // labels: ["Completion"],
    labels: [t("Completion")],
  };

  const chartSeries = [totalCompletion];

  return (
    <>
      {!isLoading ? (
        <Card className="bg-white border border-gray-200 rounded-lg w-full shadow-md">
          <div className="bg-red-600 h-2 w-full rounded-t-lg"></div>
          <CardHeader className="bg-red-100 ">
            <CardTitle className="text-lg font-semibold text-red-700">
              {t("Completion")}
            </CardTitle>
          </CardHeader>
          <CardContent className="pt-0">
            {hasData ? (
              <ResponsiveContainer width="100%" height={280}>
                <div className="flex flex-col items-center justify-center mt-2">
                  <ReactApexChart
                    options={chartOptions}
                    series={chartSeries}
                    type="radialBar"
                    height={170}
                  />
                  <div className="items-center pl-12">
                    {skillData.map((item, index) => (
                      <div key={index} className="flex items-left space-x-2">
                        <span
                          className="w-3 h-3 mt-1"
                          style={{ backgroundColor: "#008FFB" }}
                        ></span>
                        <span className="text-xs font-normal text-gray-600 pb-1">
                          {item.name}: {item.value.toFixed(2)}%
                        </span>
                      </div>
                    ))}
                  </div>
                </div>
              </ResponsiveContainer>
            ) : (
              <div className="flex flex-col items-center justify-center h-[290px]">
                <p className="text-gray-500 text-lg font-medium pb-10">
                  {t("No data available")}
                </p>
              </div>
            )}
          </CardContent>
        </Card>
      ) : (
        <Spinner />
      )}
    </>
  );
};

export default CompletionCardGraph;
