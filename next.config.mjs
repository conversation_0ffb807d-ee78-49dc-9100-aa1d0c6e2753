import i18nextConfig from './next-i18next.config.js';
/** @type {import('next').NextConfig} */
const nextConfig = {
  images: {
    remotePatterns: [
      {
        protocol: "https",
        hostname: "kfpwszxuivydjftikxts.supabase.co",
      },
      {
        protocol: "https",
        hostname: "vwzvqyvnuxpfiosqmeps.supabase.co",
      },
      {
        protocol: "https",
        hostname: "tsghdsndkborjlbxzfyb.supabase.co",
      },
      {
        protocol: "https",
        hostname: "adgglpxysanovczgltzl.supabase.co",
      },
      {
        protocol: "https",
        hostname: "ozuhxwfkkwhpmgliseia.supabase.co",
      }


      
    ],
  },
   i18n: i18nextConfig.i18n, 
  experimental: {
    // Move any experimental flags here
    missingSuspenseWithCSRBailout: false,
  },
};

export default nextConfig;
