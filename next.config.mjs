/** @type {import('next').NextConfig} */
const nextConfig = {
  images: {
    remotePatterns: [
      {
        protocol: "https",
        hostname: "kfpwszxuivydjftikxts.supabase.co",
      },
      {
        protocol: "https",
        hostname: "vwzvqyvnuxpfiosqmeps.supabase.co",
      },
      {
        protocol: "https",
        hostname: "tsghdsndkborjlbxzfyb.supabase.co",
      },
      {
        protocol: "https",
        hostname: "adgglpxysanovczgltzl.supabase.co",
      },
      {
        protocol: "https",
        hostname: "ozuhxwfkkwhpmgliseia.supabase.co",
      }


      
    ],
  },
  experimental: {
    // Move any experimental flags here
    missingSuspenseWithCSRBailout: false,
  },
};

export default nextConfig;
