import {
  UserStatistics,
  AllResourceRequest,
  AllResourcesResult,
  CourseDetailsRequest,
  CourseDetailsResultType,
  CourseModuleRecentActivities,
  CourseProgress,
  CourseProgressRequestType,
  GetCourseProgressRequest,
  GetCourseProgressResponse,
  LoginUserData,
  MarksPerResource,
  NotificationRequest,
  PerformanceResult,
  SectionViewResultType,
  UserStatisticsReq,
  UserStatisticsResponse,
  ViewResourcePageType,
  CategoryStatsReqType,
  CategorySummaryResultType,
  CategoryPerformanceResultType,
  UpdateDocumentRequest,
  GetSessionReport,
  GetSessionReportResponse,
  SkipVideoRequest,
  SkipVideoResponse,
  CoursesParams,
} from "@/types";
import { supabase } from "../lib/client";
import { rpc, views } from "@/lib/apiConfig";
import { KEYS } from "@/lib/keys";

interface UseCourseReturn {
  courseDetails: (
    queryParams: CourseDetailsRequest
  ) => Promise<CourseDetailsResultType[]>;
  sectionDetails: (sectionId: string,courseId: string) => Promise<SectionViewResultType>;
  viewResourcePage: (
    instanceId: string,
    type: string,
    moduleId: string
  ) => Promise<ViewResourcePageType | []>;
  getAllResoures: (
    queryParams: AllResourceRequest
  ) => Promise<AllResourcesResult>;
  getUserStatics: (
    queryParams: UserStatisticsReq
  ) => Promise<UserStatisticsResponse>;
  getUserCourseStatics: (
    queryParams: UserStatisticsReq
  ) => Promise<UserStatistics[]>;
  getUserAllCourseStatics: (
    queryParams: UserStatisticsReq
  ) => Promise<UserStatistics[]>;
  getRecentActivities: (
    queryParams: UserStatisticsReq
  ) => Promise<CourseModuleRecentActivities>;
  getCourseProgressData: (queryParams: UserStatisticsReq) => Promise<CourseProgress>;
  getPerformance: (
    queryParams: UserStatisticsReq
  ) => Promise<PerformanceResult>;
  getCategorySummaryAllCourse: (
    queryParams: CategoryStatsReqType
  ) => Promise<CategoryPerformanceResultType>;
  updateCourseProgress: (
    queryParams: CourseProgressRequestType
  ) => Promise<PerformanceResult>;
  getCourseProgress: (queryParams: GetCourseProgressRequest)=> Promise <GetCourseProgressResponse[]>;
  getAllCourseDatas: (queryParams: NotificationRequest)=> Promise <UserStatistics[]>;
  updateDocumentProgress: (
    queryParams: UpdateDocumentRequest
  ) => Promise<PerformanceResult>;
  getSessionReport: (
    queryParams: GetSessionReport
  ) => Promise<GetSessionReportResponse>;
  skipResource: (
    queryParams: SkipVideoRequest
  ) => Promise<SkipVideoResponse>;
  listCourses: (topicId?: string | null) => Promise<CoursesParams[]>;
}

export const useCourse = (): UseCourseReturn => {
  async function courseDetails(
    queryParams: CourseDetailsRequest
  ): Promise<CourseDetailsResultType[]> {
    const { data, error } = await supabase.rpc<string, null>(
      rpc.getCourseDetails,
      queryParams
    );
    if (error) {
      throw new Error(error.details);
    }
    return data as CourseDetailsResultType[];
  }

  async function sectionDetails(
    sectionId: string,
    courseId: string
  ): Promise<SectionViewResultType> {
    try {
      const orgId = localStorage.getItem(KEYS.ORG_ID);
      const userDetails = localStorage.getItem("userDetails");

      if (userDetails !== null && userDetails !== undefined) {
        const userInfo = JSON.parse(userDetails) as LoginUserData;
        const requestBody = {
          user_id: userInfo.id,
          section_id: sectionId,
          course_id: courseId,
          org_id: orgId
        };
        const { data, error } = await supabase.rpc<string, null>(
          rpc.getSectionDetails,
          requestBody
        );
        if (error) {
          throw new Error(error.details);
        }

        if (Array.isArray(data) && data.length > 0) {
          return data[0] as SectionViewResultType;
        }
      }
      return {} as SectionViewResultType;
    } catch (error) {
      console.error("Error:", error);
      throw error;
    }
  }

  async function viewResourcePage(
    type: string,
    instanceId: string,
    moduleId: string
  ): Promise<ViewResourcePageType | []> {
    const requestBody: {
      org_id?: string;
      url_id?: string;
      file_id?: string;
      page_id?: string;
      quiz_id?: string;
      course_module_id?: string;
      user_id?: string;
    } = {};
    const orgId = localStorage.getItem(KEYS.ORG_ID);
    if (orgId !== null && type != "Quiz") {
      requestBody.org_id = orgId;
    }

    let fromInstance = "";

    if (type == "Url" || type == "URL" || type == "url") {
      fromInstance = "fn_get_course_resource_url";
      const userDetails = localStorage.getItem("userDetails");
      if (userDetails !== null && userDetails !== undefined) {
        const userInfo = JSON.parse(userDetails) as LoginUserData;
        requestBody.user_id = userInfo.id;

      }

      requestBody.url_id = instanceId;
    } else if (type == "Page" || type == "page") {
      fromInstance = "fn_get_view_resource_page";

      requestBody.page_id = instanceId;
    } else if (type == "Quiz") {
      fromInstance = "get_questions_of_quiz";
      // You can set other properties for Quiz here
      requestBody.quiz_id = instanceId;
    } else if (type == "File" || type == "file" ) {
   
      fromInstance = "fn_get_view_resource_file";

      requestBody.file_id = instanceId;
    }
      requestBody.course_module_id = moduleId;
    try {
      if (fromInstance !== null && fromInstance !== undefined) {
        const { data, error } = await supabase.rpc<string, null>(
          fromInstance,
          requestBody
        );
        if (error) {
          throw new Error(error.details);
        }

        if (Array.isArray(data) && data.length > 0) {
          // Return the first item as CourseDetailsResultType
          return data[0] as ViewResourcePageType;
        } else {
          return data as ViewResourcePageType;
        }
      } else {
        return [];
      }
    } catch (error) {
      console.error("Error:", error);
      throw error;
    }
  }
  async function getAllResoures(
    queryParams: AllResourceRequest
  ): Promise<AllResourcesResult> {
    const { data, error } = await supabase.rpc<string, null>(
      rpc.getCourseResourceDetails,
      queryParams
    );
    if (error) {
      throw new Error(error.details);
    }
    return data as AllResourcesResult;
  }

  async function getUserStatics(
    queryParams: UserStatisticsReq
  ): Promise<UserStatisticsResponse> {
    const { data, error } = await supabase.rpc<string, null>(
      rpc.userStatistics,
      queryParams
    );
    if (error) {
      throw new Error(error.details);
    }
    return data as UserStatisticsResponse;
  }

  async function getUserCourseStatics(
    queryParams: UserStatisticsReq
  ): Promise<UserStatistics[]> {
    const { data, error } = await supabase.rpc<string, null>(
      rpc.userCourseStatistics,
      queryParams
    );
    if (error) {
      throw new Error(error.details);
    }
    return data as UserStatistics[];
  }

  async function getUserAllCourseStatics(
    queryParams: UserStatisticsReq
  ): Promise<UserStatistics[]> {
    const { data, error } = await supabase.rpc<string, null>(
      rpc.getAllCoursesData,
      queryParams
    );
    if (error) {
      throw new Error(error.details);
    }
    return data as UserStatistics[];
  }
  
  async function getRecentActivities(
    queryParams: UserStatisticsReq
  ): Promise<CourseModuleRecentActivities> {
    const { data, error } = await supabase.rpc<string, null>(
      rpc.recentActivities,
      queryParams
    );
    if (error) {
      throw new Error(error.details);
    }
    return data as CourseModuleRecentActivities;
  }

  async function getCourseProgressData(
    queryParams: UserStatisticsReq
  ): Promise<CourseProgress> {
    const { data, error } = await supabase.rpc<string, null>(
      rpc.courseCompletionProgress,
      queryParams
    );
    if (error) {
      throw new Error(error.details);
    }
    return data as CourseProgress;
  }
  async function getPerformance(
    queryParams: UserStatisticsReq
  ): Promise<PerformanceResult> {
    const { data, error } = await supabase.rpc<string, null>(
      rpc.getExamPerformance,
      queryParams
    );
    if (error) {
      throw new Error(error.details);
    }
    return data as PerformanceResult;
  }

  async function getCategorySummaryAllCourse(
    queryParams: UserStatisticsReq
  ): Promise<CategoryPerformanceResultType> {
    const { data, error } = await supabase.rpc<string, null>(
      rpc.getCategoryPerformanceAllCourses,
      queryParams
    );
    if (error) {
      throw new Error(error.details);
    }
    return data as CategoryPerformanceResultType;
  }

  async function updateCourseProgress(
    queryParams: CourseProgressRequestType
  ): Promise<PerformanceResult> {
    const { data, error } = await supabase.rpc<string, null>(
      rpc.setCourseProgress,
      queryParams
    );
    if (error) {
      throw new Error(error.details);
    }
    return data as PerformanceResult;
  }
  async function getCourseProgress(
    queryParams: GetCourseProgressRequest
  ): Promise<GetCourseProgressResponse[]> {
    const { data, error } = await supabase.rpc<string, null>(
      rpc.getCourseProgress,
      queryParams
    );
    if (error) {
      throw new Error(error.details);
    }
    return data as GetCourseProgressResponse[];
  }

  async function getAllCourseDatas(
    queryParams: NotificationRequest
  ): Promise<UserStatistics[]> {
    const { data, error } = await supabase.rpc<string, null>(
      rpc.getAllCoursesData,
      queryParams
    );
    if (error) {
      throw new Error(error.details);
    }
    return data as UserStatistics[];
  }
  async function updateDocumentProgress(
    queryParams: UpdateDocumentRequest
  ): Promise<PerformanceResult> {
    const { data, error } = await supabase.rpc<string, null>(
      rpc.updateDocumentProgress,
      queryParams
    );
    if (error) {
      throw new Error(error.details);
    }
    return data as PerformanceResult;
  }
  async function getSessionReport(
    queryParams:GetSessionReport
  ): Promise<GetSessionReportResponse> {
    const { data, error } = await supabase.rpc<string, null>(
      rpc.getCheckpointSessionReport,
      queryParams
    );
    if (error) {
      throw new Error(error.details);
    }
    return data as GetSessionReportResponse;
  }
  async function skipResource(
    queryParams: SkipVideoRequest
  ): Promise<SkipVideoResponse> {
    const { data, error } = await supabase.rpc<string, null>(
      rpc.skipResource,
      queryParams
    );
    if (error) {
      throw new Error(error.details);
    }
    return data as SkipVideoResponse;
  }
    async function listCourses(
    topicId?: string | null,
  ): Promise<CoursesParams[]> {
    try {
      const courseView = views?.course ?? "";
      const org_id = localStorage.getItem("orgId");
      const exeQuery = supabase
        .from(courseView)
        .select()
        .eq("org_id", org_id)
        .order("created_at", { ascending: false });

      if (topicId !== null && topicId !== "" && topicId !== undefined) {
        await exeQuery.eq("category_id", topicId);
      }
      const { data, error } = await exeQuery;
      if (error) {
        throw new Error(error.details);
      }

      return data as CoursesParams[];
    } catch (error) {
      console.error("Error:", error);
      throw error;
    }
  }
  return {
    courseDetails,
    sectionDetails,
    viewResourcePage,
    getAllResoures,
    getUserStatics,
    getUserCourseStatics,
    getUserAllCourseStatics,
    getRecentActivities,
    getCourseProgressData,
    getPerformance,
    updateCourseProgress,
    getCourseProgress,
    updateDocumentProgress,
    getAllCourseDatas,
    getCategorySummaryAllCourse,
    getSessionReport,
    skipResource,
    listCourses
  };
};
