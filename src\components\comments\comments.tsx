import React, { useEffect, useState } from "react";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Ta<PERSON>, Ta<PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import Image from "next/image";
import useComments from "@/hooks/useComments";
import { CommentsResponse } from "@/types";
import emoji from "react-easy-emoji";
import { KEYS } from "@/lib/keys";
import { Modal } from "@/components/ui/modal";
import { useTranslation } from "next-i18next";
import { CheckCheck, Clock3, Reply, X } from "lucide-react";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import CommentsModal from "../comment-modal";

const INITIAL_COMMENTS_COUNT = 3;

interface CommentProps {
  id: string;
  commentsData: CommentsResponse[];
  onCommentAdded?: () => void;
}

const CommentsSection = ({
  id,
  commentsData,
  onCommentAdded,
}: CommentProps) => {
  const { t } = useTranslation("common");
  const { getComments } = useComments();
  const [showMore, setShowMore] = useState(false);
  const [visibleComments, setVisibleComments] = useState<CommentsResponse[]>(
    []
  );
  const [activeTab, setActiveTab] = useState("feedback");
  const [userId, setUserId] = useState<string>("");
  const [commentId, setCommentId] = useState<string>("");
  const [commentType, setCommentType] = useState<string>("");
  const [isOpenComments, setIsOpenComments] = useState<boolean>(false);
  const [parentId, setParentId] = useState<string>("");
  const [childToParentMap, setChildToParentMap] = useState<
    Record<string, string>
  >({});
  const [feedbackComments, setFeedbackComments] = useState<CommentsResponse[]>(
    []
  );
  const [suggestionComments, setSuggestionComments] = useState<
    CommentsResponse[]
  >([]);
  const [feedbackCount, setFeedbackCount] = useState<CommentsResponse[]>([]);
  const [suggestionCount, setSuggestionCount] = useState<CommentsResponse[]>(
    []
  );

  useEffect(() => {
    const USER_DATA = localStorage.getItem(KEYS.USER_DETAILS);
    let currentUserId = "";
    if (USER_DATA !== null) {
      const userInfo = JSON.parse(USER_DATA);
      currentUserId = userInfo.id;
      setUserId(userInfo.id);
    }

    const filterVisible = (comment: CommentsResponse) => {
      return comment.status !== "Rejected" || comment.user_id === currentUserId;
    };

    const feedbacks = commentsData
      .filter((comment) => comment.type === "Feedback")
      .filter(filterVisible);
    setFeedbackComments(feedbacks);

    const suggestions = commentsData
      .filter((comment) => comment.type === "Suggestion")
      .filter(filterVisible);
    setSuggestionComments(suggestions);

    const feedbackCommentsCount = commentsData
      .flatMap((comment) => [comment, ...(comment.children || [])])
      .filter((comment) => comment.type === "Feedback");
    setFeedbackCount(feedbackCommentsCount);

    const suggestionCommentsCount = commentsData
      .flatMap((comment) => [comment, ...(comment.children || [])])
      .filter((comment) => comment.type === "Suggestion");
    setSuggestionCount(suggestionCommentsCount);

    const initialComments = activeTab === "feedback" ? feedbacks : suggestions;
    setVisibleComments(initialComments.slice(0, INITIAL_COMMENTS_COUNT));
    setShowMore(false);
  }, [activeTab, commentsData]);

  useEffect(() => {
    const map: Record<string, string> = {};

    commentsData.forEach((parent) => {
      if (parent.children && Array.isArray(parent.children)) {
        parent.children.forEach((child) => {
          map[child.id] = parent.id;
        });
      }
    });

    setChildToParentMap(map);
  }, [commentsData]);

  const formatDate = (isoDate: string): string => {
    const date = new Date(isoDate);
    return date.toLocaleDateString("en-GB", {
      day: "2-digit",
      month: "short",
      year: "numeric",
    });
  };

  const toggleComments = () => {
    const currentComments =
      activeTab === "feedback" ? feedbackComments : suggestionComments;
    setVisibleComments(
      showMore
        ? currentComments.slice(0, INITIAL_COMMENTS_COUNT)
        : currentComments
    );
    setShowMore(!showMore);
  };

  const closeComments = () => {
    setIsOpenComments(false);
  };

  const flattenComments = (comments: CommentsResponse[]) => {
    const flat: CommentsResponse[] = [];
    comments.forEach((comment) => {
      flat.push(comment);
      if (comment.children && Array.isArray(comment.children)) {
        flat.push(...comment.children);
      }
    });
    return flat;
  };

  const renderCommentItem = (
    item: CommentsResponse,
    level: number = 0
  ): JSX.Element => {
    const isCurrentUser = item.user_id === userId;
    const isReply = level > 0;

    return (
      <div
        key={item.id}
        className={`${
          isReply ? "ml-[30px] max-w-[calc(100%-30px)] ml-auto" : "w-full"
        } space-y-4`}
      >
        <Card
          className={`p-4 w-full mx-auto ${
            isCurrentUser ? "bg-gray-100" : ""
          } text-[var(--color-font-color)]`}
        >
          <div className="flex gap-4">
            {/* Avatar */}
            <div className="flex-none">
              <Image
                src={
                  item.avatar_url?.startsWith("http://") ||
                  item.avatar_url?.startsWith("https://")
                    ? item.avatar_url
                    : "/assets/profile.png"
                }
                alt=""
                width={45}
                height={45}
                className="rounded-full w-[45px] h-[45px] object-cover"
              />
            </div>

            {/* Text Content */}
            <div className="flex-1 space-y-2">
              <div className="flex items-center justify-between ">
                <div className="font-semibold">{item.name}</div>

                <TooltipProvider>
                  <span className="ml-2">
                    {item.status === "Pending" && (
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Clock3 className="w-4 h-4 text-yellow-500 cursor-pointer" />
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>Pending</p>
                        </TooltipContent>
                      </Tooltip>
                    )}
                    {item.status === "Approved" && (
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <CheckCheck className="w-4 h-4 text-green-600 cursor-pointer" />
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>Approved</p>
                        </TooltipContent>
                      </Tooltip>
                    )}
                    {item.status === "Rejected" && (
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <X className="w-4 h-4 text-red-500 cursor-pointer" />
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>Rejected</p>
                        </TooltipContent>
                      </Tooltip>
                    )}
                  </span>
                </TooltipProvider>
              </div>

              <div className="flex justify-between items-center ">
                <div className="text-sm emoji">{emoji(item.message)}</div>
                <div className="text-sm text-[var(--color-font-color)] ">
                  {formatDate(item.created_at)}
                </div>
              </div>
              {item.role_name === "Admin" && (
                <div className="">
                  <Button
                    className="bg-orange-100 border-none text-orange-600"
                    size="sm"
                    onClick={() => {
                      const parentIdOfComment = childToParentMap[item.id];
                      setCommentId(parentIdOfComment ?? item.id);
                      setCommentType(item.type);
                      setIsOpenComments(true);
                    }}
                  >
                    <Reply className="w-4 h-4 me-1" />
                    Reply
                  </Button>
                </div>
              )}
            </div>
          </div>
        </Card>

        {item.children &&
          [...item.children]
            .sort(
              (a, b) =>
                new Date(a.created_at).getTime() -
                new Date(b.created_at).getTime()
            )
            .map((child) => renderCommentItem(child, level + 1))}
      </div>
    );
  };

  const renderCommentsList = (comments: CommentsResponse[]) => {
    const sortedComments = [...comments].sort(
      (a, b) =>
        new Date(a.created_at).getTime() - new Date(b.created_at).getTime()
    );

    const visible = showMore
      ? sortedComments
      : sortedComments.slice(-INITIAL_COMMENTS_COUNT);

    return (
      <div className="space-y-4 w-full ">
        {comments.length > 0 ? (
          <>
            <div className="max-h-[400px] overflow-y-auto rounded-md overflow-hidden text-[var(--color-font-color)]">
              <div className="space-y-4 p-1">
                {visible.map((item) => renderCommentItem(item))}
              </div>
            </div>

            {comments.length > INITIAL_COMMENTS_COUNT && (
              <div className="flex justify-center ">
                <Button
                  onClick={toggleComments}
                  variant="outline"
                  className="w-full sm:w-auto "
                >
                  {showMore
                    ? t("Show Less")
                    : t("Show {{count}} More Comments", {
                        count: comments.length - INITIAL_COMMENTS_COUNT,
                      })}
                </Button>
              </div>
            )}
          </>
        ) : (
          <div className="flex h-20 items-center justify-center text-[var(--color-font-color)] ">
            {t("No comments found")}
          </div>
        )}

        {isOpenComments && (
          <Modal
            title="Add Comments"
            header=""
            openDialog={isOpenComments}
            closeDialog={closeComments}
            type="max-w-xl"
          >
            <CommentsModal
              closeDialog={closeComments}
              instanceId={id}
              isReply={true}
              commentId={commentId}
              commentType={commentType}
              onSuccess={() => {
                if (onCommentAdded) onCommentAdded();
              }}
            />
          </Modal>
        )}
      </div>
    );
  };

  return (
    <div className="rounded-lg border bg-card p-4 text-[var(--color-font-color)]">
      <Tabs defaultValue="feedback" onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-2 text-[var(--color-font-color)]">
          {/* <TabsTrigger value="feedback">
            Feedback ({feedbackCount?.length})
          </TabsTrigger> */}
          <TabsTrigger value="feedback">
            {`${t("Feedback")} (${feedbackCount?.length || 0})`}
          </TabsTrigger>

          {/* <TabsTrigger value="suggestion">
            Suggestions ({suggestionCount?.length})
          </TabsTrigger> */}
          <TabsTrigger value="suggestion">
            {`${t("Suggestion")} (${suggestionCount?.length || 0})`}
          </TabsTrigger>
        </TabsList>
        <div className="mt-4">
          <TabsContent value="feedback">
            {renderCommentsList(feedbackComments)}
          </TabsContent>
          <TabsContent value="suggestion">
            {renderCommentsList(suggestionComments)}
          </TabsContent>
        </div>
      </Tabs>
    </div>
  );
};

export default CommentsSection;
