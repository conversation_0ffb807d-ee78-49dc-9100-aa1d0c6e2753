"use client"

import React, { useEffect, useState } from 'react'
import { Icon, LucideIcon } from 'lucide-react';
import { Card, CardContent } from '../ui/card';

interface achievementCardProps {
    Icon: LucideIcon;
    Title: string;
    Value: number;
    Subtitle: string;
    ColorClass: string;
    onClick?: () => void;
}
interface animatedNumberProps {
    value: number;
}
const AnimatedNumber = ({value}: animatedNumberProps) => {
    const [count, setCount] = useState(0);
  
    useEffect(() => {
      const duration = 1000; // Animation duration
      const steps = 20; // Number of animation steps
      const stepValue: number = value / steps; // Value increment per step
      let current = 0;
  
      const timer = setInterval(() => {
        current += stepValue;
        if (current > value) {
          setCount(value);
          clearInterval(timer);
        } else {
          setCount(current);
        }
      }, duration / steps);
  
      return () => clearInterval(timer);
    }, [value]);
  
    // Return the formatted number as a string with two decimal places
    return <span>{count}</span>
  };
  
const AchievementCard = ({Icon, Title, Value, Subtitle, ColorClass, onClick}: achievementCardProps) => {
  return (
    <Card className="hover:shadow-lg transition-shadow duration-300">
    <CardContent className="p-6"    onClick={onClick}>
      <div className="flex items-center space-x-4 cursor-pointer">
        <div className={`p-3 rounded-full ${ColorClass}`}>
          <Icon className="h-6 w-6 text-white" />
        </div>
        <div>
          <div className="flex items-baseline space-x-2">
            <span className="text-2xl font-bold"><AnimatedNumber value={Value} /></span>
            <span className="text-sm text-gray-500">{Subtitle}</span>
          </div>
          <p className="text-gray-600">{Title}</p>
        </div>
      </div>
    </CardContent>
  </Card>
  )
}

export default AchievementCard
