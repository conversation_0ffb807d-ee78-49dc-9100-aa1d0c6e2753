import {
  EditProfileReturn,
  ErrorType,
  LoginFormType,
  LoginReturn,
  EditProfileFormType,
  profileImageResponse,
  SignUpRequestType,
  signUpReturn,
  ToastType,
  UserDetails,
  insertNotificationRequest,
  SendResetPasswordReturn,
  allProfileResponse,
  ConfigSettingsResponse,
} from "@/types";
import { supabase } from "../lib/client";
import { rpc, views } from "@/lib/apiConfig";
import { ERROR_MESSAGES } from "@/lib/messages";
import { Reset_PASSWORD_REDIRECT_URL } from "@/lib/constants";

interface UseAuthReturn {
  signIn: (formdata: LoginFormType) => Promise<LoginReturn[]>;
  getProfileImage: (id: string) => Promise<profileImageResponse[]>;
  getAllProfileData: (id: string) => Promise<allProfileResponse[]>;
  signUpUser: (formdata: SignUpRequestType) => Promise<signUpReturn>;
  // editProfile:(transformedData:EditProfileFormType)=>Promise<EditProfileReturn>;
  // getUpdatedProfile:(user_id:string)=>Promise<UserDetails[]>;
  insertToken: (params: insertNotificationRequest) => Promise<LoginReturn[]>;
  editProfile: (
    transformedData: EditProfileFormType
  ) => Promise<EditProfileReturn>;
  // getUpdatedProfile: (user_id: string) => Promise<UserDetails[]>;
  sendPasswordResetMail: (user_email: string) => Promise<string>;
  getConfigurationSettings: (org_id: string) => Promise<ConfigSettingsResponse>;
}
const useAuthorization = (): UseAuthReturn => {
  async function signIn(formdata: LoginFormType): Promise<LoginReturn[]> {
    try {
      const {
        error,
        data: { user, session },
      } = await supabase.auth.signInWithPassword({
        email: formdata.email,
        password: formdata.password,
      });

      if (!error) {
        if (user && session) {
          return [{ data: user, session: session, error: "" }];
        } else {
          return [{ data: "", session: "", error: "User is null." }];
        }
      } else {
        return [{ data: "", session: "", error: error.message }];
      }
    } catch (error) {
      return [{ data: "", session: "", error: "An error occurred." }];
    }
  }
  async function getProfileImage(
    userId: string
  ): Promise<profileImageResponse[]> {
    try {
      const profileImages = views?.profileDetails ?? "";
      const exeQuery = supabase
        .from(profileImages)
        .select("avatar_url")
        .eq("id", userId);

      const { data, error } = await exeQuery;
      if (error) {
        throw new Error(error.message);
      }

      return data as profileImageResponse[];
      // }
    } catch (error) {
      console.error("Error:", error);
      throw error;
    }
  }

   async function getAllProfileData(
    userId: string
  ): Promise<allProfileResponse[]> {
    try {
      const profileImages = views?.profileDetails ?? "";
      const exeQuery = supabase
        .from(profileImages)
        .select()
        .eq("id", userId);

      const { data, error } = await exeQuery;
      if (error) {
        throw new Error(error.message);
      }

      return data as allProfileResponse[];
      // }
    } catch (error) {
      console.error("Error:", error);
      throw error;
    }
  }
  async function signUpUser(
    formdata: SignUpRequestType
  ): Promise<signUpReturn> {
    try {
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_SUPABASE_URL}/auth/v1/` + views.signup,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            apikey: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY as string,
          },
          body: JSON.stringify({
            email: formdata.email,
            password: formdata.password,
            data: {
              first_name: formdata.data?.first_name,
              last_name: formdata.data?.last_name,
              phone_number: formdata.data?.phone_number,
            },
          }),
        }
      );
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || ERROR_MESSAGES.already_exists);
      }
      const signUpResponse: signUpReturn = await response.json();
      return signUpResponse;
    } catch (error: any) {
      throw error;
    }
  }
  async function editProfile(
    params: EditProfileFormType
  ): Promise<EditProfileReturn> {
    try {
      const { data, error } = (await supabase.rpc(
        rpc.updateProfile,
        params
      )) as {
        data: EditProfileReturn;
        error: ErrorType | null;
      };

      return data as EditProfileReturn;
    } catch (error) {
      console.error("Error:", error);
      throw error;
    }
  }
  // async function getUpdatedProfile(user_id: string): Promise<UserDetails[]> {
  //   try {
  //     const getProfileDetails = views?.profileDetails ?? "";
  //     const exeQuery = supabase
  //       .from(getProfileDetails)
  //       .select()
  //       .eq("id", user_id);

  //     const { data, error } = await exeQuery;
  //     if (error) {
  //       throw new Error(error.message);
  //     }

  //     return data as UserDetails[];
  //     // }
  //   } catch (error) {
  //     console.error("Error:", error);
  //     throw error;
  //   }
  // }
  async function insertToken(
    params: insertNotificationRequest
  ): Promise<LoginReturn[]> {
    try {
      const { data, error } = await supabase.rpc<string, null>(
        rpc.insertNotification,
        params
      );
      if (error) {
        throw new Error(error.details);
      }
      if (Array.isArray(data) && data.length > 0) {
        return data as LoginReturn[];
      } else {
        return [] as LoginReturn[];
      }
    } catch (error) {
      throw error;
    }
  }

  async function sendPasswordResetMail(user_email: string): Promise<string> {
    try {
      const { error } = await supabase.auth.resetPasswordForEmail(user_email, {
        redirectTo: Reset_PASSWORD_REDIRECT_URL,
      });
      if (error) {
        console.error("Error sending reset password link", error);
        return "Error sending reset password link" + error.message;
      } else {
        return "Reset Password link sent successfully";
      }
    } catch (error: any) {
      throw error;
    }
  }
  async function getConfigurationSettings(
    orgId: string
  ): Promise<ConfigSettingsResponse> {
    const params = {
     org_id : orgId
    }
    try {
      const { data, error } = (await supabase.rpc(
        rpc.getConfigSettings,
        params
      )) as {
        data: ConfigSettingsResponse;
        error: ErrorType | null;
      };

      return data as ConfigSettingsResponse;
    } catch (error) {
      console.error("Error:", error);
      throw error;
    }
  }

  return {
    signIn,
    getProfileImage,
    signUpUser,
    editProfile,
    // getUpdatedProfile,
    insertToken,
    sendPasswordResetMail,
    getAllProfileData,
    getConfigurationSettings
  };
};

export default useAuthorization;
