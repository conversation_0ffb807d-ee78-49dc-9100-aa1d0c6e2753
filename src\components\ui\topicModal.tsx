import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { SUCCESS_MESSAGES } from "@/lib/messages";

export default function ChangeTopic({
  onCancel,
  onSave,
}: {
  onSave: () => void;
  onCancel: () => void;
  isModal?: boolean;
}): React.JSX.Element {
  const handleLogoutClick = (): void => {
    onSave();
  };

  return (
    <div className="rounded p-4">
      <div className="mb-2 text-center text-lg">
        <p>{SUCCESS_MESSAGES.topicChangeMsg}</p>
      </div>
      <div className="flex justify-center items-center space-x-6 mt-4">
        <Button
          type="button"
          variant="outline"
          className="primary w-24 h-10 rounded-3xl"
          onClick={onCancel}
        >
          No
        </Button>
        <Button
          type="submit"
          className="w-24 h-10 rounded-3xl"
          onClick={handleLogoutClick}
          variant="default"
        >
          Yes
        </Button>
      </div>
    </div>
  );
}
