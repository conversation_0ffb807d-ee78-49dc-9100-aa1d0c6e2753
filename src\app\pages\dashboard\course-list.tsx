"use client";
import React, { useState } from "react";
import { Carousel } from "primereact/carousel";
import { UserStatistics } from "@/types";
import { useRouter } from "next/navigation";
import { mobResponsive } from "@/lib/constants";
import { KEYS } from "@/lib/keys";
import { ArrowUpRight, Star } from "lucide-react";
import { Button } from "@/components/ui/button";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

interface VideoResourcesProps {
  courseData: UserStatistics[];
}

export default function ViewCourses({
  courseData,
}: VideoResourcesProps): React.JSX.Element {
  const router = useRouter();
  const [flippedCards, setFlippedCards] = useState<{ [key: string]: boolean }>(
    {}
  );
  const showIndicators = courseData?.length > 5;

  const openCourseDisplay = (item: UserStatistics) => {
    localStorage.setItem(KEYS.COURSE_ID, item.course_id as string);
   if( process.env.NEXT_PUBLIC_SUBJECT_WISE_LIST === "true"){
    router.push(`/pages/section-details`);
   }
   else {
    router.push(`/pages/course-details?course_id=${item.course_id}`);
   }
    
  };

  const handleMouseEnter = (courseId: string) => {
    setFlippedCards((prev) => ({ ...prev, [courseId]: true }));
  };

  const handleMouseLeave = (courseId: string) => {
    setFlippedCards((prev) => ({ ...prev, [courseId]: false }));
  };

  const resourceTemplate = (item: UserStatistics) => {
    const isFlipped = flippedCards[item.course_id] || false;

    return (
      <div className="p-3 pt-0 flex flex-col">
        <div
          className="relative w-full h-80 cursor-pointer [perspective:1000px] mt-2"
          onMouseEnter={() => handleMouseEnter(item.course_id)}
          onMouseLeave={() => handleMouseLeave(item.course_id)}
        >
          <div
            className={`relative w-full h-full transition-all duration-700 [transform-style:preserve-3d] ${
              isFlipped ? "[transform:rotateY(-180deg)]" : ""
            }`}
          >
            {/* Front Card */}
            <div className="absolute inset-0 w-full h-full [backface-visibility:hidden]">
              <div className="h-full rounded-lg bg-white shadow-lg overflow-hidden">
                <div className="h-2 bg-orange-500" />
                <div className="h-48 bg-gradient-to-br from-orange-100 to-orange-50 flex flex-col items-center justify-center p-6 relative">
                  <h3 className="text-2xl font-bold text-gray-800 text-center leading-tight mb-2">
                    {item.course_name}
                  </h3>
                </div>
                <div className="p-4">
                  <div className="flex items-center justify-between text-sm text-gray-600 mb-2">
                    <span
                      className={`px-3 py-1 rounded-full text-sm ${
                        item.progress >= 100
                          ? "bg-green-100 text-green-800"
                          : item.progress > 0
                          ? "bg-yellow-100 text-yellow-800"
                          : "bg-gray-100 text-gray-800"
                      }`}
                    >
                      {item.progress >= 100
                        ? "Completed"
                        : item.progress > 0
                        ? "In Progress"
                        : "Not Started"}
                    </span>
                    <span className="text-gray-500">
                      {item.time_spent || "00:00:00"}
                    </span>
                  </div>
                </div>

                <div className="w-3/4 mx-auto bg-gray-200 rounded-full h-2">
                  <div
                    className={`h-2 rounded-full transition-all duration-500 ${
                      item.progress >= 100
                        ? "bg-green-500"
                        : item.progress > 0
                        ? "bg-orange-500"
                        : "bg-gray-500"
                    }`}
                    style={{ width: `${item.progress || 0}%` }}
                  />
                </div>
                {/* Progress Percentage */}
                <p className="text-center text-sm text-gray-600 mt-2">
                  {item.progress || 0}% Complete
                </p>
              </div>
            </div>

            {/* Back Card */}
            <div
              className="absolute inset-0 w-full h-full [backface-visibility:hidden] [transform:rotateY(180deg)]"
              onClick={() => openCourseDisplay(item)}
            >
              <div className="h-full rounded-lg bg-orange-500 shadow-lg p-6 text-white">
                <div className="flex flex-col h-full">
                  <h4 className="text-lg font-semibold mb-4">Course Details</h4>
                  <div className="space-y-4">
                    <div>
                      <label className="text-orange-100 text-sm block mb-1">
                        Completion
                      </label>
                      <div className="w-full bg-orange-400/30 rounded-full h-2">
                        <div
                          className="h-2 rounded-full bg-white transition-all duration-500"
                          style={{ width: `${item.progress || 0}%` }}
                        />
                      </div>
                      <p className="text-sm mt-1">
                        {item.progress || 0}% Complete
                      </p>
                    </div>

                    <div>
                      <label className="text-orange-100 text-sm block mb-1">
                        Time Spent
                      </label>
                      <p className="text-lg font-medium">
                        {item.time_spent || "00:00:00"}
                      </p>
                    </div>

                    <div>
                      <label className="text-orange-100 text-sm block mb-1">
                        Achievements
                      </label>
                      <div className="flex items-center space-x-1">
                        {[...Array(item.achievements || 0)].map((_, index) => (
                          <Star
                            key={index}
                            className="w-4 h-4 text-yellow-300 fill-current"
                          />
                        ))}
                        {!item.achievements && (
                          <span className="text-sm text-orange-100">
                            No achievements yet
                          </span>
                        )}
                      </div>
                    </div>

                    <div>
                      <label className="text-orange-100 text-sm block ">
                        Marks Scored
                      </label>
                      <p className="text-lg font-medium">
                        {item.totalMarks || "0"}
                      </p>
                    </div>
                    <span className="pt-5">

                    </span>
                  </div>
                 
                </div>
              </div>
            </div>
          </div>
        </div>
        {/* Start Training Button - Now outside the card */}
        {/* <button
          className="mt-4 bg-orange-500 text-white py-2.5 px-4 rounded-lg font-medium hover:bg-orange-600 transition-colors shadow-md hover:shadow-lg transform hover:-translate-y-0.5 duration-200 w-full max-w-[90%] mx-auto flex items-center justify-center space-x-2"
          onClick={() => openCourseDisplay(item)}
        >
          <span>Continue</span>
          <svg
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            strokeWidth="1.5"
            stroke="currentColor"
            className="w-5 h-5"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              d="M13.5 4.5l7.5 7.5m0 0l-7.5 7.5m7.5-7.5H3"
            />
          </svg>
        </button> */}
      </div>
    );
  };

  const getMaxWidth = () => {
    if (courseData.length === 0) return "max-w-[0px]";
    if (courseData.length === 1) return "max-w-[400px]";
    if (courseData.length === 2) return "max-w-[800px]";
    if (courseData.length === 3) return "max-w-[1200px]";
    if (courseData.length === 4) return "max-w-[1600px]";
    return "w-full"; //max-w-[2000px]
  };

  return (
    <div>
      <div className="mt-2 flex items-center justify-between">
        <h2 className="text-2xl text-[var(--color-font-color)] font-bold pb-0 pt-2">Courses</h2>
      </div>
      {courseData?.length > 0 ? (
        <div className={`bg-gradient-to-r rounded-2xl ${getMaxWidth()}`}>
          <Carousel
            value={courseData}
            numVisible={5}
            numScroll={2}
            itemTemplate={resourceTemplate}
            className="w-full sm:w-full" 
            showNavigators={showIndicators}
            responsiveOptions={mobResponsive}
          />
        </div>
      ) : (
        <p className="text-left text-lg text-gray-500 pt-5">No data available!</p>
      )}
    </div>
  );
}