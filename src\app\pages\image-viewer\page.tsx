"use client";
import React, { useEffect, useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { useSearchParams } from "next/navigation";
import { useRouter } from "next/navigation";
import MainLayout from "../layouts/mainLayout";
import Image from "next/image";
import CommentsSection from "@/components/comments/comments";
import { KEYS } from "@/lib/keys";
import { MessageCircle, MessageSquarePlus, ThumbsUp } from "lucide-react";
import { Modal } from "@/components/ui/modal";
import CommentsModal from "@/components/comment-modal";
import useComments from "@/hooks/useComments";
import {
  CommentsResponse,
  CourseData,
  CourseProgressRequestType,
  InnerItem,
  ToastType,
} from "@/types";
import { Card } from "@/components/ui/card";
import { Spinner } from "@/components/ui/progressiveLoder";
import { getLocalStorageItem } from "@/lib/utils";
import NextBreadcrumb from "@/components/breadcrumb";
import getBreadCrumbItems from "@/hooks/useBreadcrumbs";
import { useCourse } from "@/hooks/useCourse";
import { SUCCESS_MESSAGES } from "@/lib/messages";
import { useToast } from "@/components/ui/use-toast";
import { UseLogClass } from "@/hooks/useLog";
import { UUID } from "@/lib/constants";

const ImageViewer = (): React.JSX.Element => {
  const searchParams = useSearchParams();
  const router = useRouter();
  const getResourceData = localStorage.getItem(KEYS.RESOURCE_DATA);
  const parsedData = JSON.parse(getResourceData ?? "");
  const fileName = parsedData.name;
  const topicName = parsedData.topic_name;
  const fileUrl = parsedData.url || parsedData.external_url || parsedData.file_url;  
  const instance_id = parsedData.id;
  const courseId = searchParams?.get("course_id") as string;
  const [openFeedback, setOpenFeedback] = useState(false);
  const [commentsData, setCommentsData] = useState<CommentsResponse[]>([]);
  const [isCommentsLoding, setIsCommentsLoading] = useState<boolean>(true);
  const [breadcrumbItems, setBreadcrumbItems] = useState<InnerItem[]>([]);
  const { getComments, addComments } = useComments();
  const [courseData, setCourseData] = useState<CourseData[]>([]);
  const [likeCount, setLikeCount] = useState(0);
  const [showComments, setShowComments] = useState(false);
  const [isLiked, setIsLiked] = useState(false);
  const [userId, setUserId] = useState<string>("");
  const [LikeData, setLikeData] = useState<CommentsResponse[]>([]);
  const [commentCount, setCommentCount] = useState<number>(0);
  const topicWise = searchParams?.get("is_topic_wise");
  const sectionId = searchParams?.get("section_id");
  const { insertLogDetails } = UseLogClass();
  const { updateCourseProgress } = useCourse();
  const { toast } = useToast() as unknown as ToastType;
  const progress = searchParams?.get("progress");

  const myLoader = (): string => {
    return fileUrl;
  };
  const addFeedback = () => {
    setOpenFeedback(true);
  };

  const closeFeedback = () => {
    setOpenFeedback(false);
    getCommentData();
  };

  useEffect(() => {
    const USER_DATA = localStorage.getItem(KEYS.USER_DETAILS);
    if (USER_DATA !== null) {
      const userInfo = JSON.parse(USER_DATA);
      setUserId(userInfo.id);
    }
    const courses = getLocalStorageItem("courseData");
    if (courses) {
      const parsedData = JSON.parse(courses as string);
      setCourseData(parsedData);
    }
    getLikeData();
    getCommentData();
    checkUserLiked();
    setBreadcrumbItems(
      getBreadCrumbItems("Image Viewer", {
        course_id: courseId,
      })
    );
    insertLogDetails(
      "Course_Resource",
      "Resource Viewer",
      `${fileName} Image  Viewed `,
      "SUCCESS",
      instance_id
    );
    if (topicWise === "true") {
      setBreadcrumbItems(
        getBreadCrumbItems("Image Viewer Topicwise", {
          section_id: sectionId as string,
          course_id: courseId,
        })
      );
    } else {
      setBreadcrumbItems(
        getBreadCrumbItems("Image Viewer", {
          course_id: courseId,
        })
      );
    }
  }, []);

  const checkUserLiked = () => {
    const liked = LikeData.some(
      (comment) =>
        comment.user_id === userId && comment.activity_type === "like"
    );

    setIsLiked(liked);
  };

  const getLikeData = () => {
    const fetchLikeData = async (): Promise<void> => {
      const USER_DATA = localStorage.getItem(KEYS.USER_DETAILS);
      let currentUserId = "";
      if (USER_DATA !== null) {
        const userInfo = JSON.parse(USER_DATA);
        currentUserId = userInfo.id;
      }

      const org_id = localStorage.getItem(KEYS.ORG_ID);
      const params = {
        instance_id: instance_id as string,
        org_id: org_id as string,
        activity_type: "like",
      };
      try {
        const result = await getComments(params);

        setIsCommentsLoading(false);
        if (result.length > 0) {
          setLikeCount(result.length);
          setLikeData(result);
          const userHasLiked = result.some(
            (like) =>
              like.user_id === currentUserId && like.activity_type === "like"
          );
          setIsLiked(userHasLiked);
        }
      } catch (error) {
        console.log(error);
      }
    };
    fetchLikeData()
      .then((response) => {
        console.log(response);
      })
      .catch((error) => {
        console.log(error);
      });
  };

  const getCommentData = () => {
    const fetchCommentData = async (): Promise<void> => {
      const org_id = localStorage.getItem(KEYS.ORG_ID);
      const params = {
        instance_id: instance_id as string,
        org_id: org_id as string,
        activity_type: "comment",
      };
      try {
        const result = await getComments(params);

        setIsCommentsLoading(false);
        if (result) {
          setCommentCount(result.length);
          setCommentsData(result);
        }
      } catch (error) {
        console.log(error);
      }
    };
    fetchCommentData()
      .then((response) => {
        console.log(response);
      })
      .catch((error) => {
        console.log(error);
      });
  };

  const updateProgress = async (percent: string) => {
    const orgID = getLocalStorageItem(KEYS.ORG_ID);
    const userID = getLocalStorageItem(KEYS.USER_ID);
    let params: CourseProgressRequestType = {
      course_id: courseId as string,
      instance_id: instance_id,
      org_id: orgID as string,
      progress_data: { progress: "100" },
      user_id: userID as string,
    };
    try {
      const result = await updateCourseProgress(params);
      if (result) {
        localStorage.setItem(KEYS.CHECKPOINT_CANCELED, "false");
        let msg = SUCCESS_MESSAGES.progress_msg;
        toast({
          variant: "success",
          title: SUCCESS_MESSAGES.success,
          description: msg,
        });
      }
    } catch (error) {
      console.log(error);
    }
  };

  const handleLike = async () => {
    const reqParams = {
      comment_data: {
        subject: "",
        message: "",
        type: "Feedback",
        parent_id: null,
        activity_type: "like",
      },
      instance_id: instance_id,
      user_id: userId,
    };

    try {
      const result = await addComments(reqParams);

      if (result.status === "success") {
        toast({
          variant: "success",
          title: isLiked ? "Unliked!" : "Liked!",
          description: isLiked
            ? "You removed your like."
            : "You liked this material.",
        });
        setIsLiked(!isLiked);
        setLikeCount((prevCount) => (isLiked ? prevCount - 1 : prevCount + 1));
        await insertLogDetails(
          "Course_Resource",
          "Resource Viewer",
          `${fileName} Image Liked  `,
          "SUCCESS",
          result.comment_id
        );
      }
    } catch (error) {
      toast({
        variant: "destructive",
        title: "Error",
        description: "Action failed.",
      });
      await insertLogDetails(
        "Course_Resource",
        "Resource Viewer",
        `Failed to like ${fileName} Image `,
        "ERROR",
        UUID
      );
    }
  };

  const handleComments = (): void => {
    getCommentData();
    setShowComments(!showComments);
  };

  return (
    <MainLayout titleText="">
      <NextBreadcrumb
        items={breadcrumbItems}
        separator={<span> | </span>}
        containerClasses="flex py-5"
        listClasses="hover:underline mx-2 font-bold"
        capitalizeLinks
      />
      <Card className="w-full sm:w-4/5 lg:w-1/2 max-w-full mx-auto p-4 sm:p-6 text-[var(--color-font-color)]">
        <div>
          <p className="text-lg font-semibold mt-2 mb-2">Topic: {topicName}</p>
          <p className="text-lg font-semibold mt-2 mb-2">Name: {fileName}</p>
          <div className="flex flex-col lg:flex-row gap-4">
            <div className="w-full">
              <div className="rounded-md overflow-hidden">
                <Image
                  loader={myLoader}
                  src={fileUrl}
                  alt="image__preview"
                  width={800}
                  height={500}
                  layout="responsive"
                />
              </div>
            </div>
          </div>
          <div className="flex justify-end mt-4">
            {(progress !== "100" || progress === null) && (
              <Button
                variant="outline"
                className="w-full sm:w-auto px-4 py-2"
                onClick={() => {
                  updateProgress("100");
                }}
              >
                Mark as Done
              </Button>
            )}
          </div>
        </div>
        <div className="flex justify-between items-center mt-4 mb-4">
          <div className="flex items-center gap-2 ">
            <button
              onClick={handleLike}
              className="flex items-center gap-2 text-black-600 bg-gray-100 p-2 rounded transition"
              title={isLiked ? "Unlike" : "Like"}
            >
              <ThumbsUp
                className={`w-5 h-5 ${
                  isLiked ? "text-blue-600 fill-blue-600" : "text-gray-600"
                }`}
                fill={isLiked ? "currentColor" : "none"}
              />
              <span className="text-sm font-medium text-[var(--color-font-color)]">
                {likeCount} {likeCount === 1 ? "Like" : "Likes"}
              </span>
            </button>
          </div>

          <button
            onClick={handleComments}
            className="flex items-center gap-2 text-black-600 bg-gray-100 p-2 rounded transition"
          >
            <MessageCircle className="w-5 h-5 text-orange-600" />
            <span className="text-sm font-medium text-[var(--color-font-color)]">
              Comments
            </span>
          </button>
        </div>
        {showComments && (
          <div className="w-full mt-5">
            <div className="flex justify-between items-center gap-2 mb-4">
              <div className="flex items-center gap-2">
                <MessageCircle className="w-5 h-5 text-orange-600" />
                <h2 className="text-lg sm:text-xl font-semibold">Comments</h2>
              </div>
              <div className="cursor-pointer" title="Add comments">
                <MessageSquarePlus
                  color="orange"
                  size={24}
                  onClick={addFeedback}
                />
              </div>
            </div>
            {!isCommentsLoding ? (
              <div className="space-y-4 p-4 bg-gray-50 rounded-lg">
                <CommentsSection
                  id={instance_id}
                  commentsData={commentsData}
                  onCommentAdded={getCommentData}
                ></CommentsSection>
              </div>
            ) : (
              <Spinner></Spinner>
            )}
          </div>
        )}

        {openFeedback && (
          <Modal
            title="Add Comments"
            header=""
            openDialog={openFeedback}
            closeDialog={closeFeedback}
            type="max-w-xl"
          >
            <CommentsModal
              closeDialog={closeFeedback}
              instanceId={instance_id}
            />
          </Modal>
        )}
      </Card>
      <div className="flex flex-col sm:flex-row justify-end gap-2 mt-4">
        <Button
          variant="outline"
          className="w-full sm:w-auto  rounded-md hover:text-[#fff]"
          onClick={() => {
            // Check if we came from subject-details page
            const resourceData = localStorage.getItem(KEYS.RESOURCE_DATA);
            if (resourceData) {
              const parsedResourceData = JSON.parse(resourceData);
              // If the resource data has topic_name, it likely came from subject-details
              if (parsedResourceData.topic_name && topicWise === "true") {
                router.push(`/pages/subject-details?section_id=${sectionId}&course_id=${courseId}`);
                return;
              }
            }

            if (process.env.NEXT_PUBLIC_SUBJECT_WISE_LIST === "true") {
              router.push(`/pages/section-details`);
            } else {
              router.push(`/pages/course-details?course_id=${courseId}`);
            }
          }}
        >
          Back
        </Button>
      </div>
    </MainLayout>
  );
};

export default ImageViewer;
