import React, { useEffect, useState } from "react";
import {
  ACQUIRED_MARKS,
  COVERED_PERCENTAGE,
  COVERED_TIME,
  ACHIEVEMENTS,
} from "@/lib/constants";
import { CourseStatsGraphType, Statistics, UserStatistics } from "@/types";
import { Chart, registerables } from "chart.js";
Chart.register(...registerables);
import { List } from "lucide-react";

import { Bar } from "react-chartjs-2";
import { Card, CardContent, CardHeader, CardTitle } from "../ui/card";
import { Spinner } from "../ui/progressiveLoder";
import { useRouter } from "next/navigation";

export const DBUserCourseViews: React.FC<{
  courseStats: UserStatistics[];
  courseLength: number;
}> = ({ courseStats, courseLength }) => {
  const [isLoading, setIsLoading] = useState(false);
  const [coursesStatistics, setCoursesStatistics] =
    useState<CourseStatsGraphType>();
  const router = useRouter();
  useEffect(() => {
    const courseLabels: string[] = [];
    const totalMarks: number[] = [];
    const totalProgress: number[] = [];
    const totalTime: string[] = [];
    const totalAchievements: number[] = [];

    courseStats.map((item) => {
      courseLabels.push(item.course_name);
      item?.totalMarks > 0
        ? totalMarks.push(item.totalMarks)
        : totalMarks.push(0);
      item.progress > 0
        ? totalProgress.push(item.progress)
        : totalProgress.push(0);
      item.time_spent !== 0
        ? totalTime.push(String(item.time_spent))
        : totalTime.push("0");
      item.achievements > 0
        ? totalAchievements.push(item.achievements)
        : totalAchievements.push(0);
    });

    const graphData = {
      labels: courseLabels,
      datasets: [
        {
          data: totalMarks,
          label: ACQUIRED_MARKS,
          backgroundColor: "#1ea185",
          borderWidth: 2,
        },
        {
          data: totalProgress,
          label: COVERED_PERCENTAGE,
          backgroundColor: "#9bbb5c",
          borderWidth: 2,
        },
        {
          data: totalTime,
          label: COVERED_TIME,
          backgroundColor: "#f29b26",
          borderWidth: 2,
        },

        {
          data: totalAchievements,
          label: ACHIEVEMENTS,
          backgroundColor: "#bd392f",
          borderWidth: 2,
        },
      ],
    };

    setCoursesStatistics(graphData as CourseStatsGraphType);
    setIsLoading(false);
  }, []);

  const handleShowAll = () => {
    router.push(`/pages/course-statistics`);
  };
  return (
    <>
      <Card className="bg-white border-none">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-lg font-medium">
            Course wise statistics
          </CardTitle>
          <List
            color=""
            className="h-8 w-8 mr-2 text-accent-foreground/50"
            aria-hidden="true"
          />
          {courseLength > 5 && (
            <span
              className="text-sm text-blue-500 underline cursor-pointer"
              onClick={handleShowAll} // Replace with your actual show all function
            >
              Show All
            </span>
          )}
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <Spinner />
          ) : (
            <div className="mt-4 ">
              <div className="w-full bg-white rounded-md duration-500 ">
                {coursesStatistics !== null &&
                coursesStatistics !== undefined ? (
                  <Bar
                    data={coursesStatistics as CourseStatsGraphType}
                    height={300}
                    options={{
                      maintainAspectRatio: false,

                      scales: {
                        y: {
                          ticks: {
                            padding: 50,
                          },
                          title: {
                            display: true,
                            text: "Session Count",
                            padding: { top: 20, bottom: -30 },
                          },
                        },
                      },
                      plugins: {
                        datalabels: {
                          display: function (context) {
                            return (
                              context.dataset.data[context.dataIndex] !== 0
                            ); 
                          },
                          color: "black",
                          formatter: Math.round,
                          anchor: "end",
                          offset: -20,
                          align: "start",
                        },
                        legend: {
                          labels: {},
                          align: "center",
                          position: "bottom",
                        },
                      },
                      onHover: function (event) {
                        const target = event.native?.target;
                        if (target instanceof HTMLElement) {
                          target.style.cursor = "";
                        }
                      },
                    }}
                  />
                ) : (
                  ""
                )}
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </>
  );
};
export default DBUserCourseViews;
