"use client";
import React, { useEffect, useState } from 'react';
import { <PERSON>, <PERSON>Content, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Search, BookOpen, Clock, Award, Users, Plus, Eye, Edit, CheckCircle, XCircle, TrendingUp, Calendar, User } from 'lucide-react';
import MainLayout from '../layouts/mainLayout';
import { supabase } from '@/lib/client';
import { rpc } from '@/lib/apiConfig';
import { useCourse } from '@/hooks/useCourse';
import { CoursesParams } from '@/types';
import { useThemeColors } from '@/hooks/useThemeColors';
import { getLocalStorageItem } from '@/lib/utils';
import { useTranslation } from 'next-i18next';

interface Course {
  id: string;
  name: string;
  description: string;
  status: string;
  start_date: string;
  end_date: string;
  organization_id: string;
  created_at: string;
  updated_at: string;
}

interface CourseRequest {
  id: string;
  course_id: string;
  user_id: string;
  status: 'pending' | 'approved' | 'rejected';
  requested_at: string;
  course_name: string;
  user_name: string;
}

export default function CoursesPage() {
  const [requests, setRequests] = useState<CourseRequest[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [isLoading, setIsLoading] = useState(true);
  const [activeTab, setActiveTab] = useState<'courses' | 'requests'>('courses');
  const { listCourses } = useCourse();
  const [courses, setCourses] = React.useState<CoursesParams[] | undefined>([]);
  const { t } = useTranslation("common");

  // Apply theme colors
  const savedTheme = getLocalStorageItem("theme");
  useThemeColors(savedTheme ?? "light");

  useEffect(() => {
    fetchCourseData();
  }, []);

  const fetchCourseData = async (): Promise<void> => {
    try {
      const topicId = null;
      const data = await listCourses(topicId);
      const sortedData = data;
      setIsLoading(false);
      setCourses(sortedData);
    } catch (error) {
      setIsLoading(!isLoading);
    }
  };

  const handleRequestAction = async (requestId: string, action: 'approve' | 'reject') => {
    try {
      const { error } = await supabase
        .from('course_requests')
        .update({ 
          status: action === 'approve' ? 'approved' : 'rejected',
          updated_at: new Date().toISOString()
        })
        .eq('id', requestId);

      if (error) throw error;
      await fetchCourseData();
    } catch (error) {
      console.error('Error updating request:', error);
    }
  };

  const filteredCourses = courses?.filter(course =>
    course.short_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    course.description?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const filteredRequests = requests.filter(request =>
    request.course_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    request.user_name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-[var(--color-button-primary)] text-[var(--color-button-primary-text)] border-[var(--color-button-primary)]';
      case 'inactive': return 'bg-gray-100 text-gray-800 border-gray-200';
      case 'draft': return 'bg-[var(--color-button-secondary)] text-[var(--color-button-secondary-text)] border-[var(--color-button-secondary)]';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getRequestStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'bg-[var(--color-button-info)] text-[var(--color-button-info-text)] border-[var(--color-button-info)]';
      case 'approved': return 'bg-[var(--color-toast-success)] text-white border-[var(--color-toast-success)]';
      case 'rejected': return 'bg-[var(--color-toast-error)] text-white border-[var(--color-toast-error)]';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  return (
    <MainLayout titleText="">
      <div className="min-h-screen bg-[var(--color-background)]">
        <div className="max-w-7xl mx-auto px-4 py-6 md:px-6 md:py-8">
          {/* Header Section */}
          <div className="mb-8">
            <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6">
              <div>
                <h1 className="text-3xl md:text-4xl font-bold text-[var(--color-font-color)] mb-3">
                  {t("Courses Management")}
                </h1>
                <p className="text-gray-600 text-base md:text-lg">
                  {t("Manage your courses and handle enrollment requests")}
                </p>
              </div>
              <Button className="px-6 py-3 text-base font-medium transition-all duration-300 shadow-lg hover:shadow-xl">
                <Plus className="h-5 w-5 mr-2" />
                {t("Add Course")}
              </Button>
            </div>
          </div>

          {/* Stats Cards */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            <Card className="bg-white border border-gray-200 shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1 hover:border-[var(--color-button-primary)] group">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-gray-500 text-sm font-semibold mb-2 uppercase tracking-wide">{t("Total Courses")}</p>
                    <p className="text-3xl font-bold text-[var(--color-font-color)] group-hover:text-[var(--color-button-primary)] transition-colors duration-300">{courses?.length || 0}</p>
                  </div>
                  <div className="p-3 bg-gray-50 rounded-xl group-hover:bg-[var(--color-button-primary)] group-hover:text-[var(--color-button-primary-text)] transition-all duration-300">
                    <BookOpen className="h-8 w-8 text-gray-600 group-hover:text-[var(--color-button-primary-text)] transition-colors duration-300" />
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-white border border-gray-200 shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1 hover:border-[var(--color-button-primary)] group">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-gray-500 text-sm font-semibold mb-2 uppercase tracking-wide">{t("Active Courses")}</p>
                    <p className="text-3xl font-bold text-[var(--color-font-color)] group-hover:text-[var(--color-button-primary)] transition-colors duration-300">
                      {courses?.filter(c => c.status === 'active').length || 0}
                    </p>
                  </div>
                  <div className="p-3 bg-gray-50 rounded-xl group-hover:bg-[var(--color-button-primary)] group-hover:text-[var(--color-button-primary-text)] transition-all duration-300">
                    <TrendingUp className="h-8 w-8 text-gray-600 group-hover:text-[var(--color-button-primary-text)] transition-colors duration-300" />
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-white border border-gray-200 shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1 hover:border-[var(--color-button-primary)] group">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-gray-500 text-sm font-semibold mb-2 uppercase tracking-wide">{t("Pending Requests")}</p>
                    <p className="text-3xl font-bold text-[var(--color-font-color)] group-hover:text-[var(--color-button-primary)] transition-colors duration-300">
                      {requests.filter(r => r.status === 'pending').length}
                    </p>
                  </div>
                  <div className="p-3 bg-gray-50 rounded-xl group-hover:bg-[var(--color-button-primary)] group-hover:text-[var(--color-button-primary-text)] transition-all duration-300">
                    <Users className="h-8 w-8 text-gray-600 group-hover:text-[var(--color-button-primary-text)] transition-colors duration-300" />
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Tabs */}
          <div className="flex space-x-2 mb-8 bg-gray-50 rounded-xl p-2 shadow-sm border border-gray-200">
            <Button
              variant="default"
              onClick={() => setActiveTab('courses')}
              className={`flex-1 py-3 px-4 text-base font-semibold rounded-lg transition-all duration-300 ${
                activeTab === 'courses'
                  ? 'bg-[var(--color-button-primary)] text-[var(--color-button-primary-text)] shadow-lg transform scale-105'
                  : 'bg-white text-gray-600 hover:bg-gray-100 hover:text-[var(--color-font-color)] shadow-sm'
              }`}
            >
              <BookOpen className="h-5 w-5 mr-2" />
              {t("All Courses")} ({courses?.length || 0})
            </Button>
            <Button
              variant="default"
              onClick={() => setActiveTab('requests')}
              className={`flex-1 py-3 px-4 text-base font-semibold rounded-lg transition-all duration-300 ${
                activeTab === 'requests'
                  ? 'bg-[var(--color-button-primary)] text-[var(--color-button-primary-text)] shadow-lg transform scale-105'
                  : 'bg-white text-gray-600 hover:bg-gray-100 hover:text-[var(--color-font-color)] shadow-sm'
              }`}
            >
              <Users className="h-5 w-5 mr-2" />
              {t("Course Requests")} ({requests.filter(r => r.status === 'pending').length})
            </Button>
          </div>

          {/* Search */}
          <div className="relative mb-8">
            <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
            <Input
              placeholder={`${t("Search")} ${activeTab === 'courses' ? t('courses') : t('requests')}...`}
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-12 pr-4 py-3 bg-white border border-gray-200 rounded-lg shadow-sm focus:border-[var(--color-button-primary)] focus:ring-2 focus:ring-[var(--color-button-primary)] focus:ring-opacity-20 text-base font-medium placeholder:text-gray-400 transition-all duration-300 text-[var(--color-font-color)]"
            />
          </div>

          {isLoading ? (
            <div className="text-center py-16">
              <div className="animate-spin rounded-full h-12 w-12 border-4 border-gray-200 border-t-[var(--color-button-primary)] mx-auto"></div>
              <p className="mt-4 text-[var(--color-font-color)] text-lg">{t("Loading your courses...")}</p>
            </div>
          ) : (
            <>
              {activeTab === 'courses' && (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {filteredCourses?.map((course) => (
                    <Card
                      key={course.id}
                      className="group bg-white border border-gray-200 shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1 hover:border-[var(--color-button-primary)]"
                    >
                      <CardHeader className="pb-4">
                        <div className="flex justify-between items-start">
                          <CardTitle className="text-xl font-bold text-[var(--color-font-color)] group-hover:text-[var(--color-button-primary)] transition-colors duration-300">
                            {course.short_name}
                          </CardTitle>
                          <Badge className={`${getStatusColor(course.status)} border font-semibold px-3 py-1 text-xs uppercase tracking-wide`}>
                            {course.status}
                          </Badge>
                        </div>
                      </CardHeader>
                      <CardContent className="pb-6">
                        <p className="text-gray-600 mb-6 line-clamp-3 leading-relaxed text-sm">
                          {course.description || t('No description available')}
                        </p>

                        <div className="space-y-3 mb-6">
                          <div className="flex items-center gap-3 text-sm text-gray-500">
                            <div className="p-2 bg-gray-50 rounded-lg group-hover:bg-[var(--color-button-primary)] group-hover:text-[var(--color-button-primary-text)] transition-all duration-300">
                              <Calendar className="h-4 w-4" />
                            </div>
                            <span className="font-medium">{t("Course ID")}: {course.id}</span>
                          </div>
                          <div className="flex items-center gap-3 text-sm text-gray-500">
                            <div className="p-2 bg-gray-50 rounded-lg group-hover:bg-[var(--color-button-primary)] group-hover:text-[var(--color-button-primary-text)] transition-all duration-300">
                              <User className="h-4 w-4" />
                            </div>
                            <span className="font-medium">{t("Status")}: {course.status}</span>
                          </div>
                        </div>

                        <div className="flex gap-3">
                          <Button
                            variant="outline"
                            size="sm"
                            className="flex-1 transition-all duration-300 py-2 font-medium"
                          >
                            <Eye className="h-4 w-4 mr-2" />
                            {t("View")}
                          </Button>
                          <Button
                            variant="default"
                            size="sm"
                            className="flex-1 transition-all duration-300 py-2 font-medium"
                          >
                            <Edit className="h-4 w-4 mr-2" />
                            {t("Edit")}
                          </Button>
                        </div>
                      </CardContent>
                    </Card>
                  ))}

                  {filteredCourses?.length === 0 && (
                    <div className="col-span-full text-center py-16">
                      <div className="p-12 bg-gray-50 rounded-xl border border-gray-200 shadow-sm">
                        <BookOpen className="h-16 w-16 text-gray-400 mx-auto mb-6" />
                        <h3 className="text-2xl font-bold text-[var(--color-font-color)] mb-3">{t("No courses found")}</h3>
                        <p className="text-gray-500 text-base">{t("Try adjusting your search terms or add a new course.")}</p>
                      </div>
                    </div>
                  )}
                </div>
              )}

              {activeTab === 'requests' && (
                <div className="space-y-6">
                  {filteredRequests.map((request) => (
                    <Card
                      key={request.id}
                      className="bg-white border border-gray-200 shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1 hover:border-[var(--color-button-primary)]"
                    >
                      <CardContent className="p-6">
                        <div className="flex flex-col lg:flex-row lg:justify-between lg:items-start gap-6">
                          <div className="flex-1">
                            <h3 className="font-bold text-2xl text-[var(--color-font-color)] mb-4">{request.course_name}</h3>
                            <div className="space-y-3 text-gray-600">
                              <p className="flex items-center gap-3 text-base">
                                <div className="p-2 bg-gray-50 rounded-lg">
                                  <User className="h-5 w-5" />
                                </div>
                                <span className="font-medium">{t("Requested by")}: {request.user_name}</span>
                              </p>
                              <p className="flex items-center gap-3 text-base">
                                <div className="p-2 bg-gray-50 rounded-lg">
                                  <Calendar className="h-5 w-5" />
                                </div>
                                <span className="font-medium">{t("Requested on")}: {new Date(request.requested_at).toLocaleDateString()}</span>
                              </p>
                            </div>
                          </div>
                          <div className="flex flex-col sm:flex-row items-start sm:items-center gap-4">
                            <Badge className={`${getRequestStatusColor(request.status)} border font-semibold px-4 py-2 text-sm uppercase tracking-wide`}>
                              {request.status}
                            </Badge>
                            {request.status === 'pending' && (
                              <div className="flex gap-3">
                                <Button
                                  variant="default"
                                  size="sm"
                                  onClick={() => handleRequestAction(request.id, 'approve')}
                                  className="px-4 py-2 text-sm font-semibold transition-all duration-300 shadow-md hover:shadow-lg"
                                >
                                  <CheckCircle className="h-4 w-4 mr-2" />
                                  {t("Approve")}
                                </Button>
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() => handleRequestAction(request.id, 'reject')}
                                  className="px-4 py-2 text-sm font-semibold transition-all duration-300 shadow-md hover:shadow-lg"
                                >
                                  <XCircle className="h-4 w-4 mr-2" />
                                  {t("Reject")}
                                </Button>
                              </div>
                            )}
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}

                  {filteredRequests.length === 0 && (
                    <div className="text-center py-16">
                      <div className="p-12 bg-gray-50 rounded-xl border border-gray-200 shadow-sm">
                        <Users className="h-16 w-16 text-gray-400 mx-auto mb-6" />
                        <h3 className="text-2xl font-bold text-[var(--color-font-color)] mb-3">{t("No requests found")}</h3>
                        <p className="text-gray-500 text-base">{t("All requests have been processed or no new requests are available.")}</p>
                      </div>
                    </div>
                  )}
                </div>
              )}
            </>
          )}
        </div>
      </div>
    </MainLayout>
  );
}