"use client";
import React, { useEffect, useState } from 'react';
import { <PERSON>, <PERSON>Content, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Search, BookOpen, Clock, Award, Users, Plus, Eye, Edit, CheckCircle, XCircle, TrendingUp, Calendar, User } from 'lucide-react';
import MainLayout from '../layouts/mainLayout';
import { supabase } from '@/lib/client';
import { rpc } from '@/lib/apiConfig';
import { useCourse } from '@/hooks/useCourse';
import { CoursesParams } from '@/types';

interface Course {
  id: string;
  name: string;
  description: string;
  status: string;
  start_date: string;
  end_date: string;
  organization_id: string;
  created_at: string;
  updated_at: string;
}

interface CourseRequest {
  id: string;
  course_id: string;
  user_id: string;
  status: 'pending' | 'approved' | 'rejected';
  requested_at: string;
  course_name: string;
  user_name: string;
}

export default function CoursesPage() {
  const [requests, setRequests] = useState<CourseRequest[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [isLoading, setIsLoading] = useState(true);
  const [activeTab, setActiveTab] = useState<'courses' | 'requests'>('courses');
  const { listCourses } = useCourse();
  const [courses, setCourses] = React.useState<CoursesParams[] | undefined>([]);

  useEffect(() => {
    fetchCourseData();
  }, []);

  const fetchCourseData = async (): Promise<void> => {
    try {
      const topicId = null;
      const data = await listCourses(topicId);
      const sortedData = data;
      setIsLoading(false);
      setCourses(sortedData);
    } catch (error) {
      setIsLoading(!isLoading);
    }
  };

  const handleRequestAction = async (requestId: string, action: 'approve' | 'reject') => {
    try {
      const { error } = await supabase
        .from('course_requests')
        .update({ 
          status: action === 'approve' ? 'approved' : 'rejected',
          updated_at: new Date().toISOString()
        })
        .eq('id', requestId);

      if (error) throw error;
      await fetchCourseData();
    } catch (error) {
      console.error('Error updating request:', error);
    }
  };

  const filteredCourses = courses?.filter(course =>
    course.short_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    course.description?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const filteredRequests = requests.filter(request =>
    request.course_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    request.user_name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-black text-white border-black';
      case 'inactive': return 'bg-gray-100 text-gray-800 border-gray-200';
      case 'draft': return 'bg-white text-black border-black';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getRequestStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'bg-white text-black border-black';
      case 'approved': return 'bg-black text-white border-black';
      case 'rejected': return 'bg-gray-100 text-gray-800 border-gray-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  return (
    <MainLayout titleText="Courses Management">
      <div className="min-h-screen bg-white">
        <div className="container mx-auto px-6 py-12">
          {/* Header Section */}
          <div className="mb-12">
            <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6">
              <div>
                <h1 className="text-5xl font-bold text-black mb-3">
                  Courses Management
                </h1>
                <p className="text-gray-600 text-lg">
                  Manage your courses and handle enrollment requests
                </p>
              </div>
              <Button className="bg-black hover:bg-gray-800 text-white px-8 py-4 text-lg font-medium transition-all duration-300 shadow-lg hover:shadow-xl">
                <Plus className="h-5 w-5 mr-3" />
                Add Course
              </Button>
            </div>
          </div>

          {/* Stats Cards */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-12">
            <Card className="bg-white border border-gray-200 shadow-xl hover:shadow-2xl transition-all duration-500 hover:-translate-y-2 hover:border-black group">
              <CardContent className="p-10">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-gray-500 text-sm font-semibold mb-3 uppercase tracking-wide">Total Courses</p>
                    <p className="text-5xl font-extrabold text-black group-hover:text-black transition-colors duration-300">{courses?.length || 0}</p>
                  </div>
                  <div className="p-5 bg-gray-50 rounded-2xl group-hover:bg-black group-hover:text-white transition-all duration-300">
                    <BookOpen className="h-10 w-10 text-black group-hover:text-white transition-colors duration-300" />
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-white border border-gray-200 shadow-xl hover:shadow-2xl transition-all duration-500 hover:-translate-y-2 hover:border-black group">
              <CardContent className="p-10">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-gray-500 text-sm font-semibold mb-3 uppercase tracking-wide">Active Courses</p>
                    <p className="text-5xl font-extrabold text-black group-hover:text-black transition-colors duration-300">
                      {courses?.filter(c => c.status === 'active').length || 0}
                    </p>
                  </div>
                  <div className="p-5 bg-gray-50 rounded-2xl group-hover:bg-black group-hover:text-white transition-all duration-300">
                    <TrendingUp className="h-10 w-10 text-black group-hover:text-white transition-colors duration-300" />
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-white border border-gray-200 shadow-xl hover:shadow-2xl transition-all duration-500 hover:-translate-y-2 hover:border-black group">
              <CardContent className="p-10">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-gray-500 text-sm font-semibold mb-3 uppercase tracking-wide">Pending Requests</p>
                    <p className="text-5xl font-extrabold text-black group-hover:text-black transition-colors duration-300">
                      {requests.filter(r => r.status === 'pending').length}
                    </p>
                  </div>
                  <div className="p-5 bg-gray-50 rounded-2xl group-hover:bg-black group-hover:text-white transition-all duration-300">
                    <Users className="h-10 w-10 text-black group-hover:text-white transition-colors duration-300" />
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Tabs */}
          <div className="flex space-x-4 mb-12 bg-gray-50 rounded-3xl p-4 shadow-inner border border-gray-200">
            <Button
              variant="default"
              onClick={() => setActiveTab('courses')}
              className={`flex-1 py-6 px-8 text-lg font-semibold rounded-2xl transition-all duration-300 ${
                activeTab === 'courses' 
                  ? 'bg-black text-white shadow-2xl transform scale-105' 
                  : 'bg-white text-gray-600 hover:bg-gray-100 hover:text-black shadow-lg'
              }`}
            >
              <BookOpen className="h-6 w-6 mr-3" />
              All Courses ({courses?.length || 0})
            </Button>
            <Button
              variant="default"
              onClick={() => setActiveTab('requests')}
              className={`flex-1 py-6 px-8 text-lg font-semibold rounded-2xl transition-all duration-300 ${
                activeTab === 'requests' 
                  ? 'bg-black text-white shadow-2xl transform scale-105' 
                  : 'bg-white text-gray-600 hover:bg-gray-100 hover:text-black shadow-lg'
              }`}
            >
              <Users className="h-6 w-6 mr-3" />
              Course Requests ({requests.filter(r => r.status === 'pending').length})
            </Button>
          </div>

          {/* Search */}
          <div className="relative mb-12">
            <Search className="absolute left-8 top-1/2 transform -translate-y-1/2 text-gray-400 h-7 w-7" />
            <Input
              placeholder={`Search ${activeTab === 'courses' ? 'courses' : 'requests'}...`}
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-20 pr-8 py-8 bg-white border-2 border-gray-200 rounded-3xl shadow-xl focus:border-black focus:ring-0 text-xl font-medium placeholder:text-gray-400 transition-all duration-300 hover:shadow-2xl"
            />
          </div>

          {isLoading ? (
            <div className="text-center py-20">
              <div className="animate-spin rounded-full h-16 w-16 border-4 border-black border-t-transparent mx-auto"></div>
              <p className="mt-6 text-gray-600 text-xl">Loading your courses...</p>
            </div>
          ) : (
            <>
              {activeTab === 'courses' && (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-10">
                  {filteredCourses?.map((course, index) => (
                    <Card 
                      key={course.id} 
                      className="group bg-white border border-gray-200 shadow-xl hover:shadow-2xl transition-all duration-700 hover:-translate-y-3 hover:border-black hover:bg-gray-50"
                    >
                      <CardHeader className="pb-6">
                        <div className="flex justify-between items-start">
                          <CardTitle className="text-2xl font-bold text-black group-hover:text-black transition-colors duration-300">
                            {course.short_name}
                          </CardTitle>
                          <Badge className={`${getStatusColor(course.status)} border-2 font-semibold px-4 py-2 text-sm uppercase tracking-wide`}>
                            {course.status}
                          </Badge>
                        </div>
                      </CardHeader>
                      <CardContent className="pb-8">
                        <p className="text-gray-600 mb-8 line-clamp-3 leading-relaxed text-base">
                          {course.description || 'No description available'}
                        </p>
                        
                        <div className="space-y-4 mb-10">
                          <div className="flex items-center gap-4 text-sm text-gray-500">
                            <div className="p-3 bg-gray-50 rounded-full group-hover:bg-black group-hover:text-white transition-all duration-300">
                              <Calendar className="h-5 w-5" />
                            </div>
                            <span className="font-medium">Course ID: {course.id}</span>
                          </div>
                          <div className="flex items-center gap-4 text-sm text-gray-500">
                            <div className="p-3 bg-gray-50 rounded-full group-hover:bg-black group-hover:text-white transition-all duration-300">
                              <User className="h-5 w-5" />
                            </div>
                            <span className="font-medium">Status: {course.status}</span>
                          </div>
                        </div>

                        <div className="flex gap-4">
                          <Button 
                            variant="default" 
                            size="sm" 
                            className="flex-1 bg-white border-2 border-gray-200 text-black hover:bg-black hover:text-white hover:border-black transition-all duration-300 py-4 font-semibold shadow-lg hover:shadow-xl"
                          >
                            <Eye className="h-5 w-5 mr-2" />
                            View
                          </Button>
                          <Button 
                            variant="default" 
                            size="sm" 
                            className="flex-1 bg-black text-white hover:bg-gray-800 border-2 border-black transition-all duration-300 py-4 font-semibold shadow-lg hover:shadow-xl"
                          >
                            <Edit className="h-5 w-5 mr-2" />
                            Edit
                          </Button>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                  
                  {filteredCourses?.length === 0 && (
                    <div className="col-span-full text-center py-24">
                      <div className="p-16 bg-gray-50 rounded-3xl border-2 border-gray-100 shadow-xl">
                        <BookOpen className="h-24 w-24 text-gray-400 mx-auto mb-8" />
                        <h3 className="text-3xl font-bold text-black mb-4">No courses found</h3>
                        <p className="text-gray-500 text-xl">Try adjusting your search terms or add a new course.</p>
                      </div>
                    </div>
                  )}
                </div>
              )}

              {activeTab === 'requests' && (
                <div className="space-y-8">
                  {filteredRequests.map((request, index) => (
                    <Card 
                      key={request.id} 
                      className="bg-white border border-gray-200 shadow-xl hover:shadow-2xl transition-all duration-500 hover:-translate-y-2 hover:border-black"
                    >
                      <CardContent className="p-10">
                        <div className="flex justify-between items-start">
                          <div className="flex-1">
                            <h3 className="font-bold text-3xl text-black mb-6">{request.course_name}</h3>
                            <div className="space-y-4 text-gray-600">
                              <p className="flex items-center gap-4 text-lg">
                                <div className="p-3 bg-gray-50 rounded-full">
                                  <User className="h-6 w-6" />
                                </div>
                                <span className="font-medium">Requested by: {request.user_name}</span>
                              </p>
                              <p className="flex items-center gap-4 text-lg">
                                <div className="p-3 bg-gray-50 rounded-full">
                                  <Calendar className="h-6 w-6" />
                                </div>
                                <span className="font-medium">Requested on: {new Date(request.requested_at).toLocaleDateString()}</span>
                              </p>
                            </div>
                          </div>
                          <div className="flex items-center gap-6">
                            <Badge className={`${getRequestStatusColor(request.status)} border-2 font-semibold px-6 py-3 text-base uppercase tracking-wide`}>
                              {request.status}
                            </Badge>
                            {request.status === 'pending' && (
                              <div className="flex gap-4">
                                <Button
                                  variant="default"
                                  size="sm"
                                  onClick={() => handleRequestAction(request.id, 'approve')}
                                  className="bg-black hover:bg-gray-800 text-white px-8 py-4 text-lg font-semibold transition-all duration-300 shadow-xl hover:shadow-2xl transform hover:scale-105"
                                >
                                  <CheckCircle className="h-5 w-5 mr-3" />
                                  Approve
                                </Button>
                                <Button
                                  variant="default"
                                  size="sm"
                                  onClick={() => handleRequestAction(request.id, 'reject')}
                                  className="bg-white border-2 border-gray-200 text-black hover:bg-gray-50 hover:border-black px-8 py-4 text-lg font-semibold transition-all duration-300 shadow-xl hover:shadow-2xl transform hover:scale-105"
                                >
                                  <XCircle className="h-5 w-5 mr-3" />
                                  Reject
                                </Button>
                              </div>
                            )}
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                  
                  {filteredRequests.length === 0 && (
                    <div className="text-center py-24">
                      <div className="p-16 bg-gray-50 rounded-3xl border-2 border-gray-100 shadow-xl">
                        <Users className="h-24 w-24 text-gray-400 mx-auto mb-8" />
                        <h3 className="text-3xl font-bold text-black mb-4">No requests found</h3>
                        <p className="text-gray-500 text-xl">All requests have been processed or no new requests are available.</p>
                      </div>
                    </div>
                  )}
                </div>
              )}
            </>
          )}
        </div>
      </div>
    </MainLayout>
  );
}