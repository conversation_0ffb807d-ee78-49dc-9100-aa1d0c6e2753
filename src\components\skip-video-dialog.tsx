"use client";
import { Button } from "@/components/ui/button";
import React from "react";

const SkipModal = ({
  closeDialog,
  proceedSkip,
}: {
  closeDialog: () => void;
  proceedSkip: () => void;
}): React.JSX.Element => {
  const closeModal = () => {
    closeDialog();
  };

  return (
    <div className="">
      <p className="text-center mb-4 text-lg font-semibold">
        Do you want to skip?
      </p>

      <div className="flex justify-center gap-4">
        <Button
          
          className="w-full sm:w-auto "
          onClick={() => {
            proceedSkip();
          }}
        >
          Yes
        </Button>
        <Button type="submit"  onClick={closeModal} variant="outline">
          No
        </Button>
      </div>
    </div>
  );
};
export default SkipModal;
