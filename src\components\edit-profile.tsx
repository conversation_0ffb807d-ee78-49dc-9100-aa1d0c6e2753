"use client";
import React, { useEffect, useState } from "react";
import "../../src/styles/main.css";
import Image from "next/image";
import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
  EditProfileFormType,
  ErrorCatch,
  LoginDataReturn,
  LoginUserData,
  ProfileFormType,
} from "@/types";
import { ProfileFormSchema } from "@/schema/schema";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import useSubscriptionReport from "@/hooks/useSubscriptionReport";
import { toast } from "@/components/ui/use-toast";
import { useRouter } from "next/navigation";
import useAuthorization from "@/hooks/useAuth";
import { ERROR_MESSAGES, SUCCESS_MESSAGES } from "@/lib/messages";
import { supabase } from "@/lib/client";
import { getLocalStorageItem } from "@/lib/utils";
import { KEYS } from "@/lib/keys";
import { Label } from "@/components/ui/label";
import { UseLogClass } from "@/hooks/useLog";

export default function EditProfile({
  closeDialog,
  isDashboard,
}: {
  closeDialog: () => void;

  isDashboard: boolean;
}): React.JSX.Element {
  const router = useRouter();

  const [imageUrl, setImageUrl] = useState<string>();
  const [usersInfo, setUserInfo] = useState<LoginDataReturn>();
  const [fileURL, setfileURL] = useState<string>();
  const { editProfile, getAllProfileData } = useAuthorization();
  const [selectedImage, setSelectedImage] = useState<File | null>(null);
  const [userId, setUserId] = useState<string>("");
  const { getProfileImage } = useAuthorization();
  const { insertLogDetails } = UseLogClass();
  const [firstName, setFirstName] = useState("");
  const [lastName, setLastName] = useState("");

  const handleFileChange = async (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    const file = event.target.files?.[0];
    if (
      file &&
      (file.type === "image/png" ||
        file.type === "image/jpeg" ||
        file.type === "image/jpg")
    ) {
      if (file?.size <= 1048576) {
        setSelectedImage(file);
        const reader = new FileReader();
        reader.onloadend = () => {
          setImageUrl(reader.result as string);
        };
        reader.readAsDataURL(file);
        const fileExt = file.name.split(".").pop();
        const fileName = `${Math.random()}.${fileExt}`;
        const filePath = `${fileName}`;
        const orgID = getLocalStorageItem(KEYS.ORG_ID);
        const USER_DATA = getLocalStorageItem(KEYS.USER_DETAILS);
        if (USER_DATA) {
          const userInfo = JSON.parse(USER_DATA) as LoginUserData;
          const userId = userInfo.id;
          setUserId(userId);
          try {
            const result = await supabase.storage
              .from(`${orgID}`)
              .upload(`avatars/${userId}/${fileName}`, file, {
                cacheControl: "3600",
                upsert: true,
              });
          } catch (error) {
            console.error("Error uploading avatar: ", error);
          }
          const { data } = supabase.storage
            .from(`${orgID}`)
            .getPublicUrl(`avatars/${userId}/${fileName}`);
          setfileURL(data.publicUrl);
        } else {
          toast({
            variant: "destructive",
            title: ERROR_MESSAGES.error,
            description: "Please select a valid image file (PNG or JPG)",
          });
        }
      } else {
        toast({
          variant: "destructive",
          title: ERROR_MESSAGES.error,
          description: ERROR_MESSAGES.file_size_limit,
        });
      }
    }
  };

  const form = useForm<ProfileFormType>({
    resolver: zodResolver(ProfileFormSchema),
  });

  async function getUserImage(data: string): Promise<void> {
    try {
      const image = await getProfileImage(data);
      localStorage.setItem(KEYS.PROFILE_IMAGE, JSON.stringify(image));
      closeDialog();
      window.location.reload();
    } catch (error) {
      const err = error as ErrorCatch;
      toast({
        variant: "destructive",
        title: ERROR_MESSAGES.error,
        description: err?.message,
      });
    }
  }
  useEffect(() => {
    const firstName = localStorage.getItem("PROFILE_FNAME") ?? "";
    const lastName = localStorage.getItem("PROFILE_LNAME") ?? "";
    const phoneNumber1 = localStorage.getItem("PROFILE_PHONE_NUMBER") ?? "";
    setFirstName(firstName || "");
    setLastName(lastName || "");
  
    const userDetails = localStorage.getItem("userDetails");
    if (userDetails) {
      const userInfo = JSON.parse(userDetails);
      setUserInfo(userInfo);
  
      const emailNamePart = userInfo?.email?.split("@")[0] ?? "";
      const [emailFirstName, emailLastName] = emailNamePart.includes(".")
        ? emailNamePart.split(".")
        : [emailNamePart, ""];
  
      form.setValue(
        "firstName",
        userInfo?.user_metadata?.first_name || firstName || emailFirstName || ""
      );
  
      form.setValue(
        "lastName",
        userInfo?.user_metadata?.last_name || lastName || emailLastName || ""
      );
  
      form.setValue("email", userInfo?.email || "");
      form.setValue(
        "phoneNo",
        userInfo?.user_metadata?.phonenumber1 || phoneNumber1 || ""
      );
    }
  
    getProfileImageFromLs();
  }, []);
  

  async function onSubmit(data: ProfileFormType): Promise<void> {
    const formData = data;
    const transformedData: EditProfileFormType = {
      profile_datas: [
        {
          first_name: formData.firstName.trimEnd(),
          last_name: formData.lastName.trimEnd(),
          id: usersInfo?.id ?? "",
          avatar_url: fileURL ?? imageUrl ?? "",
          phonenumber1: formData.phoneNo,
        },
      ],
    };
    console.log("transformedData", transformedData);

    try {
      const resultProfile = await editProfile(transformedData);
      const { data, error } = await supabase.auth.updateUser({
        data: {
          first_name: formData.firstName.trimEnd(),
          last_name: formData.lastName.trimEnd(),
          phonenumber1: formData.phoneNo,
        },
      });
      if (error) {
        console.error("Error updating user:", error);
      } else {
        console.log("User updated successfully:", data);
      }

      console.log("resultProfile", resultProfile);
      if (resultProfile.status == "success") {
        toast({
          variant: "default",
          title: SUCCESS_MESSAGES.profile_update_title,
          description: SUCCESS_MESSAGES.profile_update_msg,
        });
         await insertLogDetails(
              "User",
              "Update Profile",
              `Profile Updated `,
              "SUCCESS",
               usersInfo?.id ?? "",
            );
        const result = await getAllProfileData(usersInfo?.id ?? "");
        console.log("result 124", result);

        await getUserImage(usersInfo?.id ?? "");

        if (usersInfo && usersInfo.user_metadata) {
          console.log("user data", usersInfo.user_metadata);

          usersInfo.email = result[0].email;
          usersInfo.user_metadata.email = result[0].email;
          usersInfo.user_metadata.first_name = formData.firstName; //result[0].first_name;
          usersInfo.user_metadata.last_name = formData.lastName; //result[0].last_name;
          usersInfo.user_metadata.phonenumber1 = formData.phoneNo; //result[0].phonenumber1;
          localStorage.setItem("userDetails", JSON.stringify(usersInfo));
        }
      } else {
        toast({
          variant: "destructive",
          title: ERROR_MESSAGES.error,
          description: "Something went wrong",
        });
        await insertLogDetails(
              "User",
              "Update Profile",
              `Profile Updation Failed  `,
              "ERROR",
               usersInfo?.id ?? "",
            );
      }
    } catch (error) {
      const err = error as ErrorCatch;
      toast({
        variant: "destructive",
        title: ERROR_MESSAGES.error,
        description: err?.details,
      });
        await insertLogDetails(
              "User",
              "Update Profile",
              `Profile Updation Failed  `,
              "ERROR",
               usersInfo?.id ?? "",
            );
      console.error("An unexpected error occurred:", error);
    }
  }

  const getProfileImageFromLs = (): void => {
    const profileImage = localStorage.getItem("profile_image");
    if (profileImage !== null) {
      const parsedData = JSON.parse(profileImage) as {
        avatar_url?: string;
      }[];
      const image_url = parsedData[0]?.avatar_url;
      if (image_url != null) {
        setImageUrl(image_url);
      } else {
        setImageUrl("/images/profile.png");
      }
    } else {
      setImageUrl("/images/profile.png");
    }
  };
  return (
    <div className="container mx-auto px-4 py-6">
      <div className="flex flex-col items-center">
        <div className="relative">
          <img
            src={imageUrl}
            alt="Avatar"
            className="w-40 h-40 object-cover border-2 border-white rounded-full"
          />
          <div className="absolute z-10 left-camera  border rounded-full stroke-white bg-[#FDB666] border-white">
            <Label htmlFor="file-input" style={{ cursor: "pointer" }}>
              <Image
                src="/images/profile_camera.png"
                alt="Image"
                width={50}
                height={50}
              />
            </Label>
            <Input
              id="file-input"
              type="file"
              accept="image/png, image/jpeg,image/jpg"
              style={{ display: "none" }}
              onChange={handleFileChange}
            />
          </div>
        </div>
      </div>

      <Form {...form}>
        <form
          onSubmit={(event) => void form.handleSubmit(onSubmit)(event)}
          className="grid gap-5 "
        >
          <FormField
            name="firstName"
            control={form.control}
            render={({ field }) => (
              <FormItem>
                <FormLabel className="text-[#423338]">First Name<span className="text-red-700">*</span></FormLabel>
                <FormControl>
                  <Input
                    id="firstName"
                    type="text"
                    className="border border-violet-500"
                    autoCapitalize="none"
                    autoCorrect="off"
                    {...field}
                    maxLength={50}
                    onChange={(e) => {
                      const value = e.target.value;
                      const noInitialSpace = value.replace(/^\s/, "");  // Removes only the leading space
                      form.setValue("firstName", noInitialSpace);
                    }}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            name="lastName"
            control={form.control}
            render={({ field }) => (
              <FormItem>
                <FormLabel className="text-[#423338]">Last Name<span className="text-red-700">*</span></FormLabel>
                <FormControl>
                  <Input
                    id="lastName"
                    type="text"
                    className="border border-violet-500"
                    autoCapitalize="none"
                    autoComplete="password"
                    autoCorrect="off"
                    {...field}
                    maxLength={50}
                    onChange={(e) => {
                      const value = e.target.value;
                      const sanitizedValue = value.replace(/^\s/, "")
                      form.setValue("lastName", sanitizedValue);
                    }}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            name="phoneNo"
            control={form.control}
            render={({ field }) => (
              <FormItem>
                <FormLabel className="text-[#423338]">Phone Number<span className="text-red-700">*</span></FormLabel>
                <FormControl>
                  <Input
                    id="phoneNo"
                    type="tel"
                    className="border border-violet-500"
                    autoCapitalize="none"
                    autoCorrect="off"
                    {...field}
                    maxLength={14}
                    onChange={(e) => {
                      const value = e.target.value;
                      let sanitizedValue = value.replace(/[^0-9]/g, "");
                      if (!sanitizedValue.startsWith("91")) {
                        sanitizedValue = "91" + sanitizedValue; // Ensure '+91 ' prefix
                      }
                      form.setValue(
                        "phoneNo",
                        "+91 " + sanitizedValue.substring(2)
                      );
                    }}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            name="email"
            control={form.control}
            render={({ field }) => (
              <FormItem>
                <FormLabel className="text-[#423338]">Email</FormLabel>
                <FormControl>
                  <Input
                    id="email"
                    type="email"
                    className="border border-violet-500 "
                    placeholder=""
                    autoCapitalize="none"
                    autoComplete="email"
                    autoCorrect="off"
                    {...field}
                    maxLength={50}
                    readOnly
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <div className="flex justify-center gap-4">
            {!isDashboard && (
              <Button
                type="button"
                className="rounded-lg md:w-48 md:h-12 lg:w-48 lg:h-12"
                variant="outline"
                onClick={() => {
                  console.log("Button clicked");
                  getProfileImageFromLs();
                  closeDialog();
                }}
              >
                <span className="text-base font-semibold">Cancel</span>
              </Button>
            )}

            {/* Submit button */}
            <Button
              type="submit"
              className={`rounded-lg md:w-48 md:h-12 lg:w-48 lg:h-12 ${
                isDashboard ? "w-full" : ""
              }`}
            >
              Submit
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
}
