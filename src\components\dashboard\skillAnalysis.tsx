import { COLORS } from "@/lib/constants";
import { CategoryWiseProgressType } from "@/types";
import React, { useEffect, useState } from "react";
import { Spinner } from "../ui/progressiveLoder";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardContent, Card } from "../ui/card";
import ApexCharts from "apexcharts";
// import ReactApexChart from "react-apexcharts";
import { ResponsiveContainer } from "recharts";
import dynamic from "next/dynamic";
const ReactApexChart = dynamic(() => import("react-apexcharts"), {
  ssr: false,
});
const SkillAnalysis = ({
  categorySummary,
}: {
  categorySummary: CategoryWiseProgressType[];
}) => {
  const [isLoading, setIsLoading] = useState(true);
  const [chartData, setChartData] = useState<any | null>(null);

  // Define colors array for consistency
  const chartColors = ["#008FFB",
      "#00E396",
      "#FEB019",
      "#FF4560",
      "#775DD0",
      "#546E7A",
      "#26a69a",
      "#D10CE8",];

  useEffect(() => {
    if (categorySummary && categorySummary.length > 0) {
      const seriesData = categorySummary.map((item) => item.value);
      const labels = categorySummary.map((item) => item.name);

      setChartData({
        series: seriesData,
        options: {
          chart: {
            height: 350,
            type: "radialBar",
          },
          colors: chartColors.slice(0, categorySummary.length),
          plotOptions: {
            radialBar: {
              dataLabels: {
                name: {
                  show: true,
                },
                value: {
                  show: true,
                  formatter: function (value: number) {
                    return `${value} points`; // Always show points, not percentage
                  },
                },
                total: {
                  show: true,
                  label: "Total",
                  formatter: function () {
                    return `${seriesData.reduce((acc, val) => acc + val, 0)} points`; // Ensures sum always appears in points
                  },
                },
              },
              track: {
                background: "#f2f2f2",
              },
            },
          },
          labels: labels,
          tooltip: {
            enabled: true,
            y: {
              formatter: function (value: number) {
                return `${value} points`;
              },
            },
          },
        },
      });
    }
    setIsLoading(false);
  }, [categorySummary]);

  return (
    <>
      {!isLoading ? (
        <Card className="bg-white border border-gray-200 rounded-lg w-full shadow-md">
          <div className="bg-green-600 h-2 w-full rounded-t-lg"></div>
          <CardHeader className="bg-green-100 ">
            <CardTitle className="text-lg font-semibold text-green-700">
              Skills
            </CardTitle>
          </CardHeader>
          <CardContent>
            {chartData ? (
              <ResponsiveContainer width="100%" height={290}>
                <div className="flex flex-col items-center justify-center">
                  <ReactApexChart
                    options={chartData.options}
                    series={chartData.series}
                    type="radialBar"
                    height={190}
                  />

                  {/* Legend */}
                  <div className="items-center pl-12">
                    {categorySummary?.slice(0,5)?.map((item, index) => (
                      <div key={index} className="flex items-left space-x-2 ">
                        <span
                          className="w-3 h-3"
                          style={{
                            backgroundColor: chartColors[index],
                          }}
                        ></span>
                        <span className="text-xs font-normal text-gray-600 pb-1">
                          {item.name}: {item.value.toFixed(2)} points
                        </span>
                      </div>
                    ))}
                  </div>
                </div>
              </ResponsiveContainer>
            ) : (
              <div className="flex flex-col items-center justify-center h-[290px]">
                <p className="text-gray-500 text-lg font-medium pb-10">
                  No data available!
                </p>
              </div>
            )}
          </CardContent>
        </Card>
      ) : (
        <Spinner />
      )}
    </>
  );
};

export default SkillAnalysis;