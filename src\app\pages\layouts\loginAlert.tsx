import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { useRouter } from "next/navigation";
import { ERROR_MESSAGES } from "@/lib/messages";
import { useTranslation } from "next-i18next";
export default function LoginAlert({}: {}): React.JSX.Element {
  const router = useRouter();
  const handleLoginClick = (): void => {
    router.push("/pages/login");
  };
  const { t } = useTranslation("common");
  return (
    <>
      <div className="rounded p-4 pt-0">
        <div className="mb-2 text-center text-lg">{t("token_not_found")}</div>
        <div className="flex justify-center items-center space-x-6 mt-4">
          <Button
            type="submit"
            className="text-white bg-[#9FC089] w-24 h-10 rounded-3xl"
            onClick={handleLoginClick}
          >
            {t("Proceed")}
          </Button>
        </div>
      </div>
    </>
  );
}
