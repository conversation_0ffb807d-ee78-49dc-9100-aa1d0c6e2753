.bg-total-marks {
    background: rgb(228, 74, 138);
    background: linear-gradient(90deg, rgba(228, 74, 138, 1) 0%, rgba(212, 77, 146, 1) 51%, rgba(198, 80, 158, 1) 100%);
  }
  .bg-progress {
    background: rgb(134, 79, 225);
    background: linear-gradient(90deg, rgba(134, 79, 225, 1) 0%, rgba(109, 68, 196, 1) 51%, rgba(84, 56, 166, 1) 100%);
  }
  .bg-time-spent {
    background: rgb(77, 185, 234);
    background: linear-gradient(90deg, rgba(77, 185, 234, 1) 0%, rgba(84, 173, 231, 1) 51%, rgba(92, 162, 224, 1) 100%);
  }
  .bg-achievements {
    background: rgb(255, 178, 46);
    background: linear-gradient(90deg, rgba(255, 178, 46, 1) 0%, rgba(251, 162, 62, 1) 51%, rgba(247, 141, 79, 1) 100%);
  }
  .bg-total-course {
    background: rgb(51,190,162);
    background: linear-gradient(90deg, rgba(51,190,162,1) 0%, rgba(41,171,145,1) 50%, rgba(22,139,115,1) 100%);
  }
  