"use client";

import React, { useEffect, useState } from "react";
import { getLocalStorageItem } from "@/lib/utils";
import { useCourse } from "@/hooks/useCourse";
import { KEYS } from "@/lib/keys";
import {
  CategoryWiseProgressType,
  CourseStats,
  MarksPerResource,
  SubjectMarksType,
  SubjectProgressType,
  UserStatistics,
} from "@/types";
import ScoreCardGraph from "@/components/dashboard/scoreCard";
import SubjectsProgress from "@/components/dashboard/subjectsProgress";
import MarksCardGraph from "@/components/dashboard/marksCard";
import "../../../styles/main.css";
import CompletionCardGraph from "@/components/dashboard/completionCard";
import SkillAnalysis from "@/components/dashboard/skillAnalysis";
import CourseDetailsModal from "./courseDetailsModal";
import { Modal } from "@/components/ui/modal";
interface CourseStatistics {
  totalMarksGot: number;
  percCompleted: number;
  hoursCompleted: string;
  achievements: number;
}
interface CourseStatisticsProps {
  courseID: string;
  categoryID?: string;
}

const CourseStatisticsGraphs: React.FC<CourseStatisticsProps> = ({
  courseID,
  categoryID,
}) => {
  const {
    getUserCourseStatics,
    getCourseProgressData,

    getAllCourseDatas,
    getCategorySummaryAllCourse,
    getPerformance,
  } = useCourse();
  const orgID = getLocalStorageItem(KEYS.ORG_ID);
  const [isLoading, setIsLoading] = useState(false);
  const [isMarkLoading, setIsMarkLoading] = useState(false);
  const [isProgressLoading, setIsProgressLoading] = useState(false);
  const [courseStatisticsAll, setCourseStaticsAll] = React.useState<
    UserStatistics[]
  >([]);
  const [userStatistics, setUserStatics] = React.useState<UserStatistics[]>([]);
  const [dashboardStats, setDashboardStats] =
    React.useState<CourseStatistics>();
  const [courseListWithCounter, setCourseListWithCounter] = React.useState<
    CourseStats[]
  >([]);
  const [openModal, setIsOpenModal] = React.useState<boolean>(false);
  const [courseLength, setCourseLength] = React.useState<number>(0);
  const [courseProgress, setCourseProgress] = React.useState<
    SubjectProgressType[]
  >([]);
  const [courseMarks, setCourseMarks] = React.useState<SubjectMarksType[]>([]);
  const [marksDetails, setMarkDetails] = React.useState<UserStatistics[]>([]);
  const [skillData, setSkillData] = useState<CategoryWiseProgressType[]>([]);
  const [modalTitle, setModalTitle] = useState<string>("");

  const convertTimeStringToHours = (time: string): number => {
    // Check if the input is a string that can be converted to a number
    const timeInSeconds = Number(time);
    if (!isNaN(timeInSeconds)) {
      // If it's in seconds, convert seconds to hours by dividing by 3600
      return parseFloat((timeInSeconds / 3600).toFixed(2)); // Convert seconds to hours
    }

    // If it's not just a number, check for hh:mm:ss format
    if (typeof time !== "string" || !time.includes(":")) {
      console.error(
        "Invalid input: expected a string in 'hh:mm:ss' format or seconds"
      );
      return 0;
    }

    const timeParts = time.split(":");

    // Ensure we have exactly 3 parts (hours, minutes, seconds)
    if (timeParts.length !== 3) {
      console.error("Invalid time format: expected 'hh:mm:ss'");
      return 0;
    }

    const [hoursStr, minutesStr, secondsStr] = timeParts;

    // Parse the time components
    const hours = Number(hoursStr);
    const minutes = Number(minutesStr);
    const seconds = Number(secondsStr);

    // Check if all values are valid numbers
    if (isNaN(hours) || isNaN(minutes) || isNaN(seconds)) {
      console.error("Invalid time components: unable to convert to numbers");
      return 0;
    }

    // Calculate total hours
    return parseFloat((hours + minutes / 60 + seconds / 3600).toFixed(2));
  };
  useEffect(() => {
    console.log("categoryID", categoryID);
    if (courseID !== null) {
      getCourseStatisticsDetails(courseID);
      getCouseProgressData();
      gerPerformanceDetails(courseID);
    }
  }, []);

  // Function to convert HH:MM:SS to seconds
  const timeToSeconds = (time: string) => {
    const [hours, minutes, seconds] = time.split(":").map(Number);
    return hours * 3600 + minutes * 60 + seconds;
  };

  const getCourseStatisticsDetails = async (
    courseId: string
  ): Promise<void> => {
    try {
      let user_id = getLocalStorageItem(KEYS.USER_ID) || "";
      const response = await getUserCourseStatics({
        org_id: orgID as string,
        course_id: courseId,
        user_id: user_id as string,
      });
      let filteredData = response.filter(
        (item) =>
          item.resource_type === "file" ||
          item.quiz_type === "Main" ||
          item.resource_type === "url" ||
          item.resource_type === "page"
      );

      console.log("123", filteredData);
      setCourseStaticsAll(filteredData);
      const subjectMarks = filteredData.map((item) => ({
        subject: item.resource_name,
        marks: item.totalMarks,
        section_name: item.section_name,
      }));
      setCourseMarks(subjectMarks);
      setIsLoading(true);

      setCourseLength(response?.length);
      const firstFiveItems = response.slice(0, 5);
      const allItems = response;

      setUserStatics(firstFiveItems);

      // calculate cumulative percentage of time
      const totalProgress = allItems.reduce(
        (sum, progress) => sum + progress.progress,
        0
      );
      const cumulativePercentage = totalProgress / allItems.length;

      // Calculate the total time spent
      const totalTimeInSeconds = allItems.reduce(
        (sum, progress) => sum + timeToSeconds(String(progress.time_spent)),
        0
      ); //firstFiveItems.map(timeToSeconds).reduce((sum, seconds) => sum + seconds, 0);

      // Convert back to HH:MM:SS format
      const totalHoursCovered = totalTimeInSeconds;

      const totalMarks =
        allItems.reduce(
          (accumulator, currentValue) =>
            accumulator + Number(currentValue.totalMarks),
          0
        ) || 0;
      const totalPercentCovered =
        allItems.reduce(
          (accumulator, currentValue) => cumulativePercentage,
          0
        ) || 0;

      const totalAchievements =
        allItems.reduce(
          (accumulator, currentValue) =>
            accumulator + Number(currentValue.achievements),
          0
        ) || 0;
      const overAllStatus = {
        totalMarksGot: totalMarks,
        percCompleted: totalPercentCovered,
        hoursCompleted: String(totalHoursCovered),
        achievements: totalAchievements,
      };

      setDashboardStats(overAllStatus as CourseStatistics);
    } catch (error) {
      setIsLoading(true);
    }
  };
  const gerPerformanceDetails = async (courseId: string): Promise<void> => {
    try {
      let user_id = getLocalStorageItem(KEYS.USER_ID) || "";
      const response = await getPerformance({
        org_id: orgID as string,
        course_id: courseId,
        user_id: user_id as string,
      });

      const newSkillsData = response.performance_summary?.question_category
        ?.map((category) => ({
          name: category.name,
          value: category.details.marks_obtained || 0,
        }))
        .filter((skill) => skill.value > 0);
      setSkillData(newSkillsData);
      console.log("skilldata", newSkillsData);
    } catch (error) {
      setIsLoading(true);
    }
  };

  const getCouseProgressData = async (): Promise<void> => {
    try {
      let user_id = getLocalStorageItem(KEYS.USER_ID) || "";

      if (courseID) {
        const response = await getCourseProgressData({
          org_id: orgID as string,
          course_id: courseID as string,
          user_id: user_id as string,
        });
        setIsProgressLoading(true);

        if (response.result) {
          const subjectPerformance = response.result.map((item) => ({
            subject: item.resource_name,
            completed: item.progress_percent,
            ongoing: 100 - item.progress_percent,
            section_name: item.section_name,
          }));
          setCourseProgress(subjectPerformance);
        }

        setIsLoading(true);
      }
    } catch (error) {
      setIsLoading(true);
    }
  };

  const getModalData = async (
    data: any,
    type: string,
    value: string,
    valuetype: string,
    title?: string
  ): Promise<void> => {
    setModalTitle(title ? title : valuetype);
    setIsOpenModal(true);
    const statsData = data?.map((stats: any) => ({
      course_name: stats[type],
      valuetype: valuetype,
      value: stats[value],
      section_name: stats.section_name,
    }));
    setCourseListWithCounter(statsData as CourseStats[]);
  };
  return (
    <div className="">
      <div className="flex flex-wrap md:flex-nowrap gap-4">
        <h2 className="text-2xl text-[#FB8500] font-bold mb-2">Analytics</h2>
      </div>

      <main className="flex flex-col">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 nexthub-grid-cols-2 gap-4">
          <div
            onClick={() =>
              getModalData(
                courseStatisticsAll,
                courseID === null ? "course_name" : "resource_name",
                "totalMarks",
                "Score"
              )
            }
            className=" cursor-pointer"
          >
            <ScoreCardGraph
              ScoreDetails={courseStatisticsAll}
              courseId={courseID}
            />
          </div>
          <div
            onClick={() =>
              getModalData(
                courseProgress,
                "subject",
                "completed",
                "Progress",
                "Completion"
              )
            }
          >
            <CompletionCardGraph SubjectProgress={courseProgress} />
          </div>
          <div
            onClick={() =>
              getModalData(skillData, "name", "value", "Value", "Skills")
            }
          >
            <SkillAnalysis categorySummary={skillData} />
          </div>
          <div
            onClick={() =>
              getModalData(courseMarks, "subject", "marks", "Marks")
            }
          >
            {/* <MarksCardGraph MarkDetails={courseMarks} /> */}
            {courseMarks?.length > 0 && (
              <MarksCardGraph MarkDetails={courseMarks} />
            )}
          </div>
          <div
            onClick={() =>
              getModalData(courseProgress, "subject", "completed", "Progress")
            }
          >
            <SubjectsProgress
              SubjectProgress={courseProgress}
            ></SubjectsProgress>
          </div>
        </div>
        {openModal && (
          <Modal
            title=""
            // {
            //   courseID === null
            //     ? "Course wise Statistics"
            //     : "Resource wise Statistics"
            // }
            header=""
            openDialog={openModal}
            closeDialog={() => {
              setIsOpenModal(false);
            }}
            type="max-w-3xl"
          >
            <CourseDetailsModal
              closeDialog={() => {
                setIsOpenModal(false);
              }}
              CourseStatistics={courseListWithCounter}
              CourseId={courseID}
              title={modalTitle}
            />
          </Modal>
        )}
      </main>
    </div>
  );
};

export default CourseStatisticsGraphs;
