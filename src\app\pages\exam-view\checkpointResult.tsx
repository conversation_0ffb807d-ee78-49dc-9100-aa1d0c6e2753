"use client";
import { But<PERSON> } from "@/components/ui/button";
import React from "react";
import { useTranslation } from "react-i18next";
export default function CkeckPointResult({
  closeDialog,
  data,
}: {
  closeDialog: () => void;
  data: string;
}): React.JSX.Element {
  const closeModal = () => {
    closeDialog();
  };
  const { t } = useTranslation();
  return (
    <div>
      <p className="text-center mb-8">
        {data === "PASSED" ? (
          <p>
            {t("Congratulations!!!")}

            <br />
            {t("You have passed the examination")}
          </p>
        ) : (
          t("You failed in the examination")
        )}
      </p>
      <Button onClick={closeModal} variant="default" className="rounded-md">
        {t("OK")}
      </Button>
    </div>
  );
}
