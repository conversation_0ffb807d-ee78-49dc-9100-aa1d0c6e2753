"use client";
import { But<PERSON> } from "@/components/ui/button";
import React from "react";

export default function CkeckPointResult({
  closeDialog,
  data,
}: {
  closeDialog: () => void;
  data: string;
}): React.JSX.Element {
  const closeModal = () => {
    closeDialog();
  };

  return (
    <div>
      <p className="text-center mb-8">
        {data === "PASSED" ? (
          <p>
            Congratulations!!!
            <br />
            You have passed the examination.
          </p>
        ) : (
          "You failed in the examination"
        )}
      </p>
     <Button
      onClick={closeModal}
      variant="default"
      className="rounded-md"
     >
      OK
     </Button>
      
    </div>
  );
}
