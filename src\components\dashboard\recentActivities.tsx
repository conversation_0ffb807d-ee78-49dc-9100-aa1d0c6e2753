import { useCourse } from "@/hooks/useCourse";
import { getLocalStorageItem } from "@/lib/utils";
import { RecentActivitiesType, RecentActivity } from "@/types";
import moment from "moment";
import React, { useEffect, useState } from "react";
import { Spinner } from "../ui/progressiveLoder";
import { KEYS } from "@/lib/keys";

interface recentActivitiesInterface {
  RecentActivities: RecentActivity[];
}

const RecentActivities = () => {
  const [filteredActivities, setFilteredActivities] = React.useState<
    RecentActivity[]
  >([]);
  const [recentActivities, setRecentActivities] = React.useState<
    RecentActivity[]
  >([]);

  const [isLoading, setIsLoading] = useState(false);
  const { getRecentActivities } = useCourse();
  const timeAgo = (date: moment.MomentInput) => {
    const now = moment();
    const activityTime = moment(date);

    // Check if the activity time is the same as now
    if (activityTime.isSame(now, "second")) {
      return "Just now"; // Or any message you want
    }

    // Otherwise, return the time difference
    return activityTime.fromNow();
  };

  useEffect(() => {
    getRecentActivity();
  }, []);

  const getRecentActivity = async (): Promise<void> => {
    const orgID = getLocalStorageItem(KEYS.ORG_ID);
    const courseID = getLocalStorageItem(KEYS.COURSE_ID);
    try {
      let user_id = getLocalStorageItem(KEYS.USER_ID) || "";
      const response = await getRecentActivities({
        org_id: orgID as string,
        course_id: courseID as string,
        user_id: user_id as string,
      });
      setIsLoading(true);
      setRecentActivities(response["Course Module Recent Activities"]);
      const RecentActivities = response["Course Module Recent Activities"];
      const filterData = RecentActivities.filter(
        (activity) => activity.time_spent !== null
      );
      setFilteredActivities(filterData);
    } catch (error) {
      setIsLoading(true);
    }
  };
  return isLoading ? (
    <div className="space-y-4">
      {filteredActivities.map((activity, index) => (
        <div
          key={index}
          className="flex items-center p-3 bg-[#E6E6FA] rounded-lg transition-colors duration-200 animate-fade-in"
          style={{ animationDelay: `${index * 100}ms` }}
        >
          {/* <activity.Icon className={`h-5 w-5 ${activity.color} mr-3`} iconNode={[]}/> */}
          <div className="flex-1">
            <p className="font-medium text-[var(--color-font-color)]">{activity.resource_name}</p>
            <p className="text-sm text-[var(--color-font-color)]">
              {timeAgo(activity.updated_hrs_ago)}
            </p>
          </div>
        </div>
      ))}
    </div>
  ) : (
    <Spinner />
  );
};

export default RecentActivities;
