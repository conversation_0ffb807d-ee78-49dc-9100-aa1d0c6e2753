// import type { Metadata } from 'next';
// import { Inter } from 'next/font/google';
 "use client"
import React, { useEffect, useState } from "react";
import "./globals.css";
import { Toaster } from "@/components/ui/toaster";
import Breadcrumb from "@/components/breadcrumb";
import { HomeIcon } from "lucide-react";
import { ThemeProvider } from "@/context/ThemeContext";
import { CustomBrandingDetails } from "@/types";
import ErrorBoundary from "@/components/errorBoundary";

// const inter = Inter({ subsets: ['latin'] });

// export const metadata: Metadata = {
//   title: 'Create Next App',
//   description: 'Generated by create next app'
// };

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}): React.JSX.Element {


  return (
    <html lang="en">
      <body> 
     <ErrorBoundary><ThemeProvider> {children}   </ThemeProvider></ErrorBoundary> 
      </body>
      <Toaster />
    </html>
  );
}
