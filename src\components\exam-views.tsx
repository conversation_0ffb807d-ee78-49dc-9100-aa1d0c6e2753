"use client";
import React, { useState, useEffect } from "react";
import MainLayout from "../app/pages/layouts/mainLayout";
import {
  CircleCheck,
  CrossIcon,
  FileText,
  FlagIcon,
  LayoutGrid,
  WalletCards,
  X,
} from "lucide-react";
import "../../src/styles/main.css";
import { useExam } from "@/hooks/useExam";
import {
  EndCheckPointResponse,
  ExamQuestionsResult,
  ExamReviewType,
  InnerItem,
  LoginUserData,
  QuestionAnswerType,
  SubmitAnswerType,
  SubmitQuizType,
  ToastType,
} from "@/types";
import { Spinner } from "@/components/ui/progressiveLoder";
import { useRouter, useSearchParams } from "next/navigation";
import { useToast } from "@/components/ui/use-toast";
import { ERROR_MESSAGES, SUCCESS_MESSAGES } from "@/lib/messages";
import { KEYS } from "@/lib/keys";
import { Modal } from "@/components/ui/modal";
import CkeckPointResult from "@/app/pages/exam-view/checkpointResult";
import moment from "moment";
import NextBreadcrumb from "./breadcrumb";
import getBreadCrumbItems from "@/hooks/useBreadcrumbs";
import { UseLogClass } from "@/hooks/useLog";
import { Button } from "./ui/button";

interface ExamViewProps {
  view: boolean;
}
export default function ExamView({ view }: ExamViewProps): React.JSX.Element {
  const [selectedOptions, setSelectedOptions] = useState<Array<Array<number>>>(
    []
  );

  const [activeStep, setActiveStep] = useState<number>(0);
  const [remainingTime, setRemainingTime] = useState<number>();
  const [isModalOpen, setIsModalOpen] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [isSumbit, setIsSumbit] = useState<boolean>(true);
  const [showDropdown, setShowDropdown] = useState<boolean>(false);
  const [dropdownValue, setDropdownValue] = useState<string>("Flag for review");
  const [examReview, setExamReview] = useState<ExamReviewType[]>([]);
  const [flaggedQuestions, setFlaggedQuestions] = useState<Set<number>>(
    new Set()
  );
  const [examData, setExamData] = useState<ExamQuestionsResult[]>([]);
  const [questAnswers, setQuestAnsweres] = useState<QuestionAnswerType[]>([]);
  const {
    getExamIntro,
    submitQuiz,
    calculateQuizGrade,
    submitAnswers,
    endCheckPointQuiz,
  } = useExam();
  const searchParams = useSearchParams();
  const totalSlides = Number(searchParams?.get("page_count")) ?? 0;
  const [quizId, setquizId] = useState<string>(
    (searchParams?.get("quiz_id") as string) ?? ""
  );
  const fileType = searchParams?.get("isDocument");
  const videoFile = searchParams?.get("isVideo");
  const [quizAttemptId, setquizAttemptId] = useState<string>(
    (searchParams?.get("quiz_attempt_id") as string) ?? ""
  );
  const [courseId, setcourseId] = useState<string>(
    searchParams?.get("course_id") as string
  );
  const sectionId = searchParams?.get("section_id") as string;

  const [isOpenModal, setIsOpenModal] = useState<boolean>(false);
  const [checkpointResult, setCheckpointResult] = useState<string>("");
  // const quizAttemptId = searchParams?.get("quiz_attempt_id") as string;
  // const quizId = searchParams?.get("quiz_id") as string;
  const isCheckpointExam = searchParams?.get("checkpoint_exam") === "true";
  const [breadcrumbItems, setBreadcrumbItems] = useState<InnerItem[]>([]);
  const router = useRouter();
  const { toast } = useToast() as ToastType;
  // const courseId = searchParams?.get("course_id") as string;
  const orgID = localStorage.getItem(KEYS.ORG_ID);
  let interval: any;
  const { insertLogDetails } = UseLogClass();
  // to restrict browser back
  useEffect(() => {
    window.history.pushState(null, "", window.location.href);
    const handlePopState = () => {
      window.history.pushState(null, "", window.location.href);
    };
    window.addEventListener("popstate", handlePopState);
    return () => {
      window.removeEventListener("popstate", handlePopState);
    };
  }, [router]);

  useEffect(() => {
    localStorage.removeItem(KEYS.ATTENDED_DATE);
    if (view) {
      const answerDetails = localStorage.getItem(KEYS.ANSWER_EVALUATION);
      if (answerDetails) {
        const questionsData = JSON.parse(answerDetails) as ExamReviewType[];
        const ansEvaluation = questionsData as ExamReviewType[];
        setExamReview(ansEvaluation);
      }
    }
    if (isCheckpointExam) {
      const quizData = JSON.parse(
        localStorage.getItem(KEYS.QUIZ_DATA) as string
      );
      if (quizData) {
        setquizId(quizData.questions[0].quest_answers[0].quiz_id);
        setquizAttemptId(quizData.quiz_attempt_id);
        setcourseId(quizData.questions[0].course_id);
        const result = quizData;
        setIsLoading(false);
        setExamData(result);
        const timeLeft = localStorage.getItem(KEYS.REMAINING_TIME);
        if (timeLeft) {
          setRemainingTime(parseInt(timeLeft));
        } else {
          setRemainingTime(result.questions[0].duration * 60);
        }
        result.questions[0].quest_answers.forEach((item: any) => {
          item.isFlagged = false;
          item.answers.forEach((data: any) => {
            data.answer_marked = false;
          });
        });
        setQuestAnsweres(
          result.questions[0].quest_answers as QuestionAnswerType[]
        );
        setSelectedOptions(
          Array(result.questions[0].quest_answers.length).fill([])
        );
      }
      setBreadcrumbItems(
        getBreadCrumbItems("Video Checkpoint Exam View", {
          quiz_id: quizId,
          quiz_attempt_id: quizAttemptId,
          course_id: courseId,
        })
      );
    } else {
      getQuestionDetails(quizId);
      if (view) {
        setBreadcrumbItems(
          getBreadCrumbItems("View Result", {
            quiz_id: quizId,
            quiz_attempt_id: quizAttemptId,
            course_id: courseId,
          })
        );
      } else {
        setBreadcrumbItems(
          getBreadCrumbItems("Exam View", {
            quiz_id: quizId,
            quiz_attempt_id: quizAttemptId,
            course_id: courseId,
          })
        );
      }
    }
  
  }, []);

  const getQuestionDetails = async (qId: string): Promise<void> => {
    try {
      const result = await getExamIntro(qId);
      setIsLoading(false);
      setExamData(result);
      const timeLeft = localStorage.getItem(KEYS.REMAINING_TIME);
      if (timeLeft) {
        setRemainingTime(parseInt(timeLeft));
      } else {
        setRemainingTime(result[0].duration * 60);
      }

      result[0].quest_answers.forEach((item: any) => {
        item.isFlagged = false;
        item.answers.forEach((data: any) => {
          data.answer_marked = false;
        });
      });
      setQuestAnsweres(result[0].quest_answers as QuestionAnswerType[]);
      setSelectedOptions(Array(result[0].quest_answers.length).fill([]));
    } catch (error) {
      toast({
        variant: "destructive",
        title: ERROR_MESSAGES.error,

        description: error as string,
      });
      setIsLoading(false);
    }
  };

  // const handleOptionSelect = (optionIndex: number) => {
  //   const newSelectedOptions = [...selectedOptions];
  //   const currentSelection = newSelectedOptions[activeStep];
  //   const selectedIndex = currentSelection.indexOf(optionIndex);

  //   if (selectedIndex === -1) {
  //     newSelectedOptions[activeStep] = [...currentSelection, optionIndex];
  //   } else {
  //     newSelectedOptions[activeStep].splice(selectedIndex, 1);
  //   }

  //   const updatedQuestAnswers = [...questAnswers];
  //   updatedQuestAnswers[activeStep].answers[optionIndex].answer_marked =
  //     !updatedQuestAnswers[activeStep].answers[optionIndex].answer_marked;

  //   setSelectedOptions(newSelectedOptions);
  //   setQuestAnsweres(updatedQuestAnswers);
  // };
  const handleOptionSelect = (optionIndex: number) => {
    questAnswers[activeStep].isFlagged = false;
    const newSelectedOptions = [...selectedOptions];

    // Set the selected option for the current step only
    if (
      newSelectedOptions[activeStep] &&
      newSelectedOptions[activeStep][0] === optionIndex
    ) {
      newSelectedOptions[activeStep] = []; // Deselect the option
    } else {
      newSelectedOptions[activeStep] = [optionIndex]; // Select the new option
    }
    const updatedQuestAnswers = [...questAnswers];
    // Toggle the marked state of the selected option
    updatedQuestAnswers[activeStep].answers.forEach((answer, index) => {
      answer.answer_marked = index === optionIndex; // Mark the selected option only
    });
    setSelectedOptions(newSelectedOptions);
    setQuestAnsweres(updatedQuestAnswers);
    handleFlagQuestion(activeStep);
  };
  const handlePrevious = () => {
    if (activeStep > 0) {
      setActiveStep(activeStep - 1);
    }
  };

  const handleNext = () => {
    if (activeStep < questAnswers.length - 1) {
      setActiveStep(activeStep + 1);
    }
  };

  useEffect(() => {
    const tabsOpenKey = KEYS.TAB_OPEN;
    const currentTabsOpen = parseInt(
      localStorage.getItem(tabsOpenKey) || "0",
      10
    );

    if (currentTabsOpen >= 1) {
      alert(ERROR_MESSAGES.tab_open_alert);
      router.push(`/pages/exams-list?course_id=${courseId}`);
      return;
    } else {
      localStorage.setItem(tabsOpenKey, (currentTabsOpen + 1).toString());
    }
    const handleUnload = () => {
      const updatedTabsOpen = parseInt(
        localStorage.getItem(tabsOpenKey) || "0",
        10
      );
      if (updatedTabsOpen > 0) {
        localStorage.setItem(tabsOpenKey, (updatedTabsOpen - 1).toString());
      }
    };

    window.addEventListener("beforeunload", handleUnload);
    return () => {
      handleUnload();
      window.removeEventListener("beforeunload", handleUnload);
    };
  }, [router, courseId]);

  useEffect(() => {
    if (remainingTime) {
      if (remainingTime > 0) {
        interval = setInterval(() => {
          setRemainingTime((prevTime: any) => prevTime - 1);
        }, 1000);

        localStorage.setItem(KEYS.REMAINING_TIME, remainingTime.toString());
        return () => clearInterval(interval);
      } else {
        return () => clearInterval(interval);
      }
    } else {
      return () => clearInterval(interval);
    }
  }, [remainingTime]);

  useEffect(() => {
    const handleCloseModal = (event: any) => {
      if (isModalOpen && event.target.classList.contains("modal-overlay")) {
        toggleModal();
      }
    };

    document.addEventListener("click", handleCloseModal);

    return () => {
      document.removeEventListener("click", handleCloseModal);
    };
  }, [isModalOpen]);

  const formatTime = (timeInSeconds: number) => {
    const hours = Math.floor(timeInSeconds / 3600);
    const minutes = Math.floor((timeInSeconds % 3600) / 60);
    const seconds = timeInSeconds % 60;
    return `${hours.toString().padStart(2, "0")}:${minutes
      .toString()
      .padStart(2, "0")}:${seconds.toString().padStart(2, "0")}`;
  };

  const getCurrentDateTime = () => {
    const now = new Date();
    return now.toLocaleString();
  };

  const toggleModal = () => {
    setIsModalOpen(!isModalOpen);
  };

  const toggleDropdown = () => {
    setShowDropdown(!showDropdown);
  };

  useEffect(() => {
    if (remainingTime === 0 && isSumbit) {
      toast({
        variant: "warning",
        title: "Warning",
        description: ERROR_MESSAGES.exam_time_exeed,
      });
      setTimeout(() => {
        handleSubmit();
      }, 3000);
      setIsSumbit(false);
    }
  });
  // const flagQuestion = () => {
  //   toggleDropdown();
  //   const updatedQuestAnswers = [...questAnswers];
  //   updatedQuestAnswers[activeStep].isFlagged =
  //     !updatedQuestAnswers[activeStep].isFlagged;
  //   setQuestAnsweres(updatedQuestAnswers);

  //   const updatedFlags = new Set(flaggedQuestions);
  //   if (updatedFlags.has(activeStep)) {
  //     updatedFlags.delete(activeStep);
  //   } else {
  //     updatedFlags.add(activeStep);
  //   }
  //   setFlaggedQuestions(updatedFlags);
  // };
  const flagQuestion = () => {
    toggleDropdown();
    const updatedQuestAnswers = [...questAnswers];
    const isCurrentlyFlagged = updatedQuestAnswers[activeStep].isFlagged;
    updatedQuestAnswers[activeStep].isFlagged = !isCurrentlyFlagged;
    const updatedFlags = new Set(flaggedQuestions);
    if (isCurrentlyFlagged) {
      updatedFlags.delete(activeStep);
      toast({
        variant: "default",
        title: SUCCESS_MESSAGES.title,
        description: SUCCESS_MESSAGES.flag_remove_msg,
      });
    } else {
      updatedFlags.add(activeStep);
      toast({
        variant: "default",
        title: SUCCESS_MESSAGES.title,
        description: SUCCESS_MESSAGES.flag_msg,
      });
    }
    setFlaggedQuestions(updatedFlags);
  };

  const handleFlagQuestion = (index: number) => {
    const updatedFlags = new Set(flaggedQuestions);
    if (updatedFlags.has(index)) {
      updatedFlags.delete(index);
    } else {
      updatedFlags.add(index);
    }
    setFlaggedQuestions(updatedFlags);
  };

  const handleSubmit = async () => {
    localStorage.removeItem(KEYS.REMAINING_TIME);
    clearInterval(interval);
    const submittedValues = questAnswers.map((item) => ({
      question_id: item.question_id,
      question_with_options: item.question_text,
      response_summary: item.answers
        ?.filter((ansInfo) => ansInfo.answer_marked === true)
        .map((obj) => obj.answer),
      selected_answer_ids: item.answers
        ?.filter((ansInfo) => ansInfo.answer_marked === true)
        .map((obj) => obj.answer_id),
    }));
    const userDetails = localStorage.getItem("userDetails");
    if (userDetails !== null && userDetails !== undefined) {
      const userInfo = JSON.parse(userDetails) as LoginUserData;
      let requestBody = {
        org_id: orgID ?? "",
        quiz_attempt_id: quizAttemptId ?? "",
        quiz_id: quizId,
        submit_datas: submittedValues,
        user_id: userInfo.id,
      };
      try {
        if (isCheckpointExam === true) {
          const response = await endCheckPointQuiz(
            requestBody as SubmitQuizType
          );
          if (response.status === "success") {
            setCheckpointResult(response.result);
            setIsOpenModal(true);
          } else {
          }
          localStorage.removeItem(KEYS.ANSWER_EVALUATION);
        } else {
          await submitQuiz(requestBody as SubmitQuizType);
          toast({
            variant: "default",
            title: SUCCESS_MESSAGES.title,
            description: SUCCESS_MESSAGES.exam_submit,
          });
          await insertLogDetails(
              "Exam",
              "Exam View",
              `Exam Submitted   `,
              "SUCCESS",
               quizId
            );

          const startDate = moment
            .utc(new Date())
            .local()
            .format("MMMM Do YYYY");
          localStorage.setItem(KEYS.ATTENDED_DATE, startDate);
          getQuizGrade().then(() => {
            router.push(
              `/pages/exam-review?quiz_id=${quizId}&quiz_attempt_id=${quizAttemptId}&course_id=${courseId}&checkpoint_exam=${isCheckpointExam}`
            );
          });
        }
      } catch (error: unknown) {
        const errMsg: string =
          typeof error === "string"
            ? error
            : error instanceof Error
            ? error.message
            : "Unknown error";

        toast({
          variant: "destructive",
          title: ERROR_MESSAGES.error,
          description: errMsg,
        });
          await insertLogDetails(
              "Exam",
              "Exam View",
              `Exam Submission Failed  `,
              "ERROR",
               quizId
            );
       
      }
    }
  };

  const getQuizGrade = async () => {
    try {
      const result = await calculateQuizGrade(quizAttemptId);
      if (result) {
        submitAllAnswers();
      }
    } catch (error: unknown) {
      const errMsg: string =
        typeof error === "string"
          ? error
          : error instanceof Error
          ? error.message
          : "Unknown error";

      toast({
        variant: "destructive",
        title: ERROR_MESSAGES.error,
        description: errMsg,
      });
    }
  };
  const submitAllAnswers = async (): Promise<void> => {
    const passData = {
      quiz_id: quizId,
      quiz_attempt_id: quizAttemptId,
    };
    try {
      let response = await submitAnswers(passData as SubmitAnswerType);
      if (response.length > 0) {
        const startDate = moment
          .utc(response[0].quiz_start_time)
          .local()
          .format("MMMM Do YYYY");
        localStorage.setItem(KEYS.ATTENDED_DATE, startDate);
      }
    } catch (error: unknown) {}
  };

  const goBackToList = (): void => {
    router.push(`/pages/exams-list?course_id=${courseId}`);
    localStorage.removeItem(KEYS.ANSWER_EVALUATION);
  };

  const handleDialogClose = (): void => {
    setIsOpenModal(false);
    if (isCheckpointExam === true) {
      const videoResources = localStorage.getItem("storedData");
      if (videoResources && videoFile === "true") {
        const resourceData = JSON.parse(videoResources);
        router.push(`/pages/video-player?title=${resourceData?.fileName}&fileURL=${resourceData?.fileUrl}&instance=${resourceData?.instance_id}&course_module
=${resourceData?.course_module_id}&checkEnable=${resourceData?.is_checkpoint_enabled}&result=${checkpointResult}&course_id=${courseId}`);
      } else if (fileType === "true") {
        router.push(
          `/pages/document-viewer?section_id=${sectionId}&result=${checkpointResult}&page_count=${totalSlides}&course_id=${courseId}`
        );
      }
    }
  };
  const clearSelections = () => {
    const resetQuestAnswers = questAnswers.map((item) => {
      item.answers.forEach((answer) => {
        answer.answer_marked = false;
      });
      return {
        ...item,
        isFlagged: false,
      };
    });

    setQuestAnsweres(resetQuestAnswers);
    setSelectedOptions(Array(questAnswers.length).fill([])); // Reset selected options
  };
  const removeHTMLTags = (html: string | undefined): React.JSX.Element => {
    if (html != null) {
      const tempDiv = document.createElement("div");
      tempDiv.innerHTML = html;
      return <div dangerouslySetInnerHTML={{ __html: tempDiv.innerHTML }} />;
    } else {
      return <div></div>;
    }
  };

  return (
    <MainLayout titleText="">
      <NextBreadcrumb
        items={breadcrumbItems}
        separator={<span> | </span>}
        containerClasses="flex py-5"
        listClasses="hover:underline mx-2 font-bold"
        capitalizeLinks
      />
      {isLoading ? (
        <Spinner />
      ) : (
        <div className="w-full flex flex-col items-center ">
          <div className="w-full bg-transparent">
            {!view && (
              <div className="w-full flex items-center justify-between mt-4">
                <div className="flex items-center space-x-4">
                  <div className="font-bold text-[var(--color-font-color)]">
                    {formatTime(remainingTime ? (remainingTime as number) : 0)}
                  </div>
                </div>
                <div className="text-center">{getCurrentDateTime()}</div>
                <div className="flex items-center space-x-4">
                  <div className="relative" title="Flag for review">
                    <WalletCards
                      className="exam-feedback-icon cursor-pointer"
                      onClick={toggleDropdown}
                    />
                    {showDropdown && (
                      <div className="absolute z-10 right-0 mt-2 w-48 common-bg-color text-white rounded-lg shadow-lg">
                        <div
                          className="px-4 py-2 cursor-pointer mt-4"
                          onClick={() => setDropdownValue("Flag for review")}
                        >
                          <div
                            className="flex items-center text-primary"
                            onClick={flagQuestion}
                          >
                            <FlagIcon className="cursor-pointer text-primary"/>
                            <span className="ml-2">
                              {questAnswers[activeStep].isFlagged
                                ? "Unflag Question"
                                : "Flag for review"}
                            </span>
                          </div>
                        </div>
                        {/* <div className="border-t border-white/50"></div> */}
                        <div className="px-4 py-2 cursor-pointer hover:bg-gray-200">
                          {/* <div className="flex items-center mt-4 mb-3">
                            <FileText />
                            <span className="ml-2">Report Question</span>
                          </div> */}
                        </div>
                      </div>
                    )}
                  </div>
                  <div title="Exam Progress">
                    {" "}
                    <LayoutGrid
                      onClick={toggleModal}
                      className="cursor-pointer text-primary"
                    />
                  </div>
                </div>
              </div>
            )}
            {!view && <hr className="w-full border-gray-400 my-4" />}
            <div className="items-center">
              <div className="flex flex-col space-y-4 mt-4">
                <div className="bg-white rounded-lg p-4 mb-4">
                  <div className="mb-2 flex items-start">
                    {/* <span
                    
                      removeHTMLTags(questAnswers[activeStep]?.name)
                      className="flex-1"
                    /> */}
                    <p className="mb-2 flex items-start text-[var(--color-font-color)] ">
                      <span className="font-bold mr-5 text-base">
                        {activeStep + 1}
                      </span>
                      {removeHTMLTags(questAnswers[activeStep]?.question_text)}
                    </p>
                  </div>
                  {!view && (
                    <div className="flex items-center justify-between mb-2">
                      <div className="font-medium text-[var(--color-font-color)] ">
                        Mark: {questAnswers[activeStep]?.default_mark}
                      </div>
                      {questAnswers[activeStep]?.penalty > 0 && (
                        <div className="font-medium text-[var(--color-font-color)]">
                          Penalty, if applicable:{" "}
                          {questAnswers[activeStep].penalty}
                        </div>
                      )}
                    </div>
                  )}
                  {!view && (
                    <div className="flex flex-col space-y-2">
                      {questAnswers[activeStep]?.answers.map(
                        (option, optIndex) => (
                          <button
                            key={optIndex}
                            className={`flex items-center px-4 py-3 rounded-md hover:bg-gray-300 border  ${
                              selectedOptions[activeStep]?.includes(optIndex)
                                ? "border-green-500"
                                : ""
                            }`}
                            onClick={() => handleOptionSelect(optIndex)}
                            style={{ fontSize: "16px" }}
                          >
                            <div
                              className={`w-10 h-10 px-3 py-2 flex items-center justify-center rounded-full border border-gray-300 mr-2 ${
                                selectedOptions[activeStep]?.includes(optIndex)
                                  ? "bg-green-500 text-white"
                                  : "bg-white text-gray"
                              }`}
                            >
                              {String.fromCharCode(65 + optIndex)}
                            </div>
                            <span className="flex-grow text-left">
                              {option?.answer}
                            </span>
                          </button>
                        )
                      )}
                    </div>
                  )}
                  {view && (
                    <div className="flex flex-col space-y-2">
                      {examReview[0]?.quest_answers?.[activeStep]?.answers.map(
                        (option, optIndex) => {
                          const isSelected = examReview[0].quest_answers?.[
                            activeStep
                          ]?.selected_answer_ids.includes(option.answer_id);
                          const isCorrect = option.is_correct_answer;

                          return (
                            <button
                              key={optIndex}
                              className={`flex items-center px-4 py-3 rounded-md hover:bg-gray-300 border ${
                                isSelected
                                  ? isCorrect
                                    ? "border-green-500"
                                    : "border-red-500"
                                  : isCorrect
                                  ? "border-green-500"
                                  : "border-gray-300"
                              }`}
                              style={{ fontSize: "16px" }}
                            >
                              <div
                                className={`w-10 h-10 px-3 py-2 flex items-center justify-center rounded-full border mr-2 ${
                                  isSelected
                                    ? isCorrect
                                      ? "bg-green-500 text-white"
                                      : "bg-red-500 text-white"
                                    : isCorrect
                                    ? "bg-green-500 text-white"
                                    : "bg-white text-gray-700"
                                }`}
                              >
                                {String.fromCharCode(65 + optIndex)}
                              </div>
                              <span className="flex-grow text-left">
                                {option?.answer}
                              </span>
                            </button>
                          );
                        }
                      )}
                      <div className="flex items-center space-x-4">
                        <span className="flex items-center space-x-1 mt-4">
                          <CircleCheck className="text-green-500" />
                          <span>Correct Answered</span>
                        </span>
                        <span className="flex items-center space-x-1 mt-4">
                          <CircleCheck className="text-red-500" />
                          <span>Wrong Answered</span>
                        </span>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
            <div className="flex justify-between px-4 py-2">
              <button
                className="text-white px-4 py-2 rounded-md ml-2"
                onClick={handlePrevious}
                disabled={activeStep === 0}
                style={{
                  backgroundColor: activeStep === 0 ? "#B8B8B8" : "#423338",
                }}
              >
                Previous
              </button>
              {view && (
                <Button
                  className="text-white px-4 py-2 rounded-md"
                  onClick={goBackToList}
                  variant="default"
                >
                  Finish
                </Button>
              )}
              {!view && activeStep === questAnswers.length - 1 && (
                <button
                  onClick={clearSelections}
                  className="bg-red-500 text-white rounded-md px-4 py-2"
                >
                  Clear All
                </button>
              )}
              {activeStep < questAnswers.length - 1 && (
                <button
                  className="text-white px-4 py-2 rounded-md"
                  onClick={handleNext}
                  style={{
                    backgroundColor:
                      activeStep === questAnswers.length - 1
                        ? "#B8B8B8"
                        : "#155264",
                  }}
                >
                  Next
                </button>
              )}

              {!view && activeStep === questAnswers.length - 1 && (
                <Button
                  className="text-white px-4 py-2 rounded-md"
                  onClick={handleSubmit}
                  variant="default"
                >
                  Submit
                </Button>
              )}
            </div>
          </div>
          {isModalOpen && (
            <div className="fixed inset-0 z-50 bg-gray-900 bg-opacity-50 flex items-center justify-end modal-overlay">
              <div className="bg-white rounded-lg w-96 overflow-y-auto shadow-lg transform translate-x-0 transition-transform duration-300 ease-in-out">
                <X
                  className="ml-auto mt-5 mr-1"
                  onClick={() => setIsModalOpen(false)}
                ></X>
                <div className="flex justify-between items-center p-4">
                  <h2 className="text-xl font-bold">Exam Progress</h2>
                </div>
                <div className="px-4 py-2 mb-10">
                  <div className="grid grid-cols-5 gap-2 mb-30">
                    {questAnswers.map((question, index) => {
                      const isSkipped =
                        index < activeStep &&
                        selectedOptions[index].length === 0;
                      const isNotAnswered =
                        index > activeStep &&
                        selectedOptions[index].length === 0;
                      const isFlagged = question.isFlagged;

                      return (
                        <div
                          key={index}
                          className={`flex items-center cursor-pointer justify-center w-10 h-10 rounded-full border-2 border-gray-300 ${
                            isFlagged
                              ? "exam-progress-review cursor-pointer text-white"
                              : selectedOptions[index].length > 0
                              ? "exam-progress-attended cursor-pointer text-white"
                              : activeStep === index
                              ? "exam-progress-select cursor-pointer text-white"
                              : isSkipped
                              ? "exam-progress-pending cursor-pointer text-gray"
                              : isNotAnswered
                              ? "bg-white text-gray cursor-pointer"
                              : "bg-white text-gray cursor-pointer"
                          }`}
                          onClick={() => {
                            setIsModalOpen(false);
                            setActiveStep(index);
                          }}
                        >
                          {index + 1}
                        </div>
                      );
                    })}
                  </div>
                  <div className="mt-20 text-gray-700">
                    <h3 className="font-bold pb-2">Quick Info</h3>
                    <ul className="list-disc list-inside">
                      <li className="flex items-center">
                        <span className="w-4 h-4 mr-2 exam-progress-attended rounded-full"></span>
                        Attended question
                      </li>
                      <li className="flex items-center">
                        <span className="w-4 h-4 mr-2 exam-progress-pending rounded-full"></span>
                        Skipped question
                      </li>
                      <li className="flex items-center">
                        <span className="w-4 h-4 mr-2 exam-progress-review rounded-full"></span>
                        Flagged for review
                      </li>
                      <li className="flex items-center">
                        <span className="w-4 h-4 mr-2 exam-progress-select rounded-full"></span>
                        Selected question
                      </li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      )}

      {isOpenModal && (
        <Modal
          title="Exam Result"
          header=""
          openDialog={isOpenModal}
          closeDialog={handleDialogClose}
          type="max-w-4xl"
        >
          <CkeckPointResult
            data={checkpointResult}
            closeDialog={handleDialogClose}
          />
        </Modal>
      )}
    </MainLayout>
  );
}
