import { FC } from "react";
import { Bar } from "react-chartjs-2";
import { CategoryScale, registerables, Chart } from "chart.js";
import { useTranslation } from "next-i18next";
Chart.register(CategoryScale);
Chart.register(...registerables);

const BarChart: FC = () => {
  const data = {
    labels: ["React", "Next.js", "TypeScript", "CSS"],
    datasets: [
      {
        label: "Completion (%)",
        data: [85, 70, 90, 60],
        backgroundColor: "rgba(54, 162, 235, 0.6)",
        borderColor: "rgba(54, 162, 235, 1)",
        borderWidth: 1,
      },
    ],
  };
  const { t } = useTranslation("common");
  return (
    <div className="p-4 bg-white rounded shadow">
      <h3 className="text-lg font-semibold mb-4"> {t("Course Completion")}</h3>
      <Bar data={data} />
    </div>
  );
};

export default BarChart;
