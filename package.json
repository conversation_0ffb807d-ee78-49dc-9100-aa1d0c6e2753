{"name": "smartlearn-web", "version": "0.1.0", "private": true, "license": "MIT", "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@heroicons/react": "^2.1.4", "@hookform/resolvers": "^3.6.0", "@radix-ui/react-accordion": "^1.2.0", "@radix-ui/react-avatar": "^1.1.1", "@radix-ui/react-checkbox": "^1.1.1", "@radix-ui/react-dialog": "^1.1.1", "@radix-ui/react-dropdown-menu": "^2.1.2", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-menubar": "^1.1.1", "@radix-ui/react-scroll-area": "^1.1.0", "@radix-ui/react-select": "^2.1.2", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-tabs": "^1.1.0", "@radix-ui/react-toast": "^1.2.1", "@radix-ui/react-tooltip": "^1.1.3", "@supabase/supabase-js": "^2.44.3", "@typescript-eslint/eslint-plugin": "^7.14.1", "apexcharts": "^4.3.0", "carosel": "^1.0.0", "chart.js": "^4.4.5", "chartjs-plugin-datalabels": "^2.2.0", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "d3": "^7.9.0", "d3-scale-chromatic": "^3.1.0", "declare": "^0.0.3", "document-viewer-ts": "^1.0.0-legacy", "embla-carousel-react": "^8.1.6", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.3", "firebase": "^10.14.0", "i18next": "^25.2.1", "i18next-browser-languagedetector": "^8.1.0", "i18next-http-backend": "^3.0.2", "lucide-react": "^0.399.0", "module": "^1.2.5", "next": "^14.2.4", "next-i18next": "^15.4.2", "pdfjs": "^2.5.3", "pdfjs-dist": "^4.4.168", "preview-office-docs": "^1.0.2", "primereact": "^10.8.3", "react": "^18.3.1", "react-apexcharts": "^1.7.0", "react-chartjs-2": "^5.2.0", "react-doc-viewer": "^0.1.13", "react-documents": "^1.2.1", "react-dom": "^18.3.1", "react-easy-emoji": "^1.8.1", "react-emoji-render": "^2.0.1", "react-google-docs-viewer": "^1.0.1", "react-google-slides": "^4.0.0", "react-hook-form": "^7.52.0", "react-html-renderer": "^0.3.3", "react-i18next": "^15.5.2", "react-icons": "^5.2.1", "react-office-viewer": "^1.0.4", "react-pdf": "^9.1.0", "react-player": "^2.16.0", "react-render-html": "^0.6.0", "react-responsive": "^10.0.0", "react-responsive-carousel": "^3.2.23", "recharts": "^2.13.0", "tailwind-merge": "^2.3.0", "tailwindcss-animate": "^1.0.7", "zod": "^3.23.8"}, "devDependencies": {"@types/d3-scale-chromatic": "^3.0.3", "@types/firebase": "^3.2.1", "@types/node": "^20", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "eslint": "^8", "eslint-config-next": "14.2.4", "husky": "^9.0.11", "postcss": "^8", "tailwindcss": "^3.4.4", "typescript": "5.1"}}