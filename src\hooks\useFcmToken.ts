"use client";
import { useEffect, useState } from "react";
import { getMessaging, getToken, onMessage } from "firebase/messaging";
import firebaseApp from "../../firebase";

const useFcmToken = () => {
  const [token, setToken] = useState("");
  const [notificationPermissionStatus, setNotificationPermissionStatus] =
    useState("");
  const [messagePayload, setMessagePayload] = useState("");
  useEffect(() => {
    const retrieveToken = async () => {
      try {
        if (typeof window !== "undefined" && "serviceWorker" in navigator) {
          const messaging = getMessaging(firebaseApp);
          const permission = await Notification.requestPermission();
          if (permission === "granted") {
            setNotificationPermissionStatus(permission);
            const currentToken = await getToken(messaging, {
              vapidKey:
                "BJAbxLEmU4ARcuYeH_54CDd2Gbh00lQIlWqD75WBmUGK1qux4o5dOGWmJE-3zKaePY62HoVDyLkGoDmbzBOLPic", // Replace with your Firebase project's VAPID key
            });
            if (currentToken) {
              setToken(currentToken);
              onMessage(messaging, (payload) => {
                setMessagePayload(payload?.notification?.body as string);
              });
            } else {
              console.log(
                "No registration token available. Request permission to generate one."
              );
            }
          }
        }
      } catch (error) {
        console.log("Error retrieving token:", error);
      }
    };

    retrieveToken();
  }, []);

  return { token, notificationPermissionStatus, messagePayload };
};

export default useFcmToken;
