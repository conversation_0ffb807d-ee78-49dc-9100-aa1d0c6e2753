"use client";
import React, { useEffect, useState } from "react";
import {
  Bell,
  Search,
} from "lucide-react";
import { UseNotification } from "@/hooks/useNotification";
import { KEYS } from "@/lib/keys";
import { getLocalStorageItem } from "@/lib/utils";
import { NotificationResponse } from "@/types";
import MainLayout from "../layouts/mainLayout";

const NotificationItem = ({
  notification,
}: {
  notification: NotificationResponse;
}) => {
  const getTimeAgo = (dateString: string) => {
    const now = new Date();
    const past = new Date(dateString);
    const diffInSeconds = Math.floor((now.getTime() - past.getTime()) / 1000);

    if (diffInSeconds < 60) return `${diffInSeconds} seconds ago`;
    if (diffInSeconds < 3600)
      return `${Math.floor(diffInSeconds / 60)} minutes ago`;
    if (diffInSeconds < 86400)
      return `${Math.floor(diffInSeconds / 3600)} hours ago`;
    if (diffInSeconds < 604800)
      return `${Math.floor(diffInSeconds / 86400)} days ago`;
    return past.toLocaleDateString();
  };

  return (
    <div className="group bg-white rounded-lg shadow-sm hover:shadow-md transition-all duration-200 p-4 border-l-4 border-violet-500">
      <div className="flex items-start gap-4">
        <div className="flex-shrink-0">
          <div className="w-10 h-10 rounded-full bg-violet-50 flex items-center justify-center">
            <Bell className="h-5 w-5 text-violet-500" />
          </div>
        </div>

        <div className="flex-1 min-w-0">
          <p className="text-gray-900 font-medium line-clamp-2">
            {notification.messaage_text || "No message content"}
          </p>

          {/* Flexbox for Time and Badge */}
          <div className="flex flex-col md:flex-row md:items-center md:justify-between mt-1">
            <span className="text-gray-500">
              {getTimeAgo(notification.created_at)}
            </span>
            {/* "New" Badge */}
            {!notification.is_read && (
              <span className="mt-1 md:mt-0 inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-violet-100 text-violet-800 ml-auto">
                New
              </span>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

const ShowAllNotification = () => {
  const [isLoading, setIsLoading] = useState(true);
  const [allMessages, setAllMessages] = useState<NotificationResponse[]>([]);
  const [filter, setFilter] = useState("all");
  const [searchQuery, setSearchQuery] = useState("");
  const { getNotifications } = UseNotification();

  useEffect(() => {
    fetchNotificationData();
  }, []);

  const fetchNotificationData = async (): Promise<void> => {
    try {
      let userID = getLocalStorageItem(KEYS.USER_ID) || "";
      const orgID = getLocalStorageItem(KEYS.ORG_ID);
      const response = await getNotifications({
        org_id: orgID as string,
        user_id: userID as string,
      });
      setAllMessages(response);
      setIsLoading(false);
    } catch (error) {
      console.error("Failed to fetch notifications:", error);
      setIsLoading(false);
    }
  };

  const filteredNotifications = allMessages?.filter((notification) => {
    const matchesSearch = notification.messaage_text
      ?.toLowerCase()
      .includes(searchQuery.toLowerCase());
    const matchesFilter =
      filter === "all" ||
      (filter === "unread" && !notification.is_read) ||
      (filter === "read" && notification.is_read);
    return matchesSearch && matchesFilter;
  });

  const unreadCount = allMessages?.filter((msg) => !msg.is_read).length;

  return (
    <MainLayout titleText={""}>
      <div className="mx-auto bg-gray-50 min-h-screen">
        <div className="bg-white border-b sticky top-0 z-10">
          <div className="mx-auto px-4 py-3 md:px-6 md:py-4">
            <div className="flex items-center justify-between mb-3 md:mb-4">
              <div className="flex items-center gap-2 md:gap-3">
                {unreadCount > 0 && (
                  <span className="px-2 py-1 bg-violet-100 text-violet-700 rounded-full text-xs font-medium">
                    {unreadCount} new
                  </span>
                )}
              </div>
              <button
                onClick={() => fetchNotificationData()}
                className="p-2 hover:bg-gray-100 rounded-full transition-colors"
              >
                {/* <RefreshCw className="h-5 w-5 text-gray-500" /> */}
              </button>
            </div>

            <div className="flex flex-col gap-3 mb-3 md:flex-row md:gap-4 md:mb-4">
              <div className="flex-1 relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                <input
                  type="text"
                  placeholder="Search notifications..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full pl-10 pr-4 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-violet-500"
                />
              </div>
              <select
                value={filter}
                onChange={(e) => setFilter(e.target.value)}
                className="px-3 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-violet-500 bg-white"
              >
                <option value="all">All</option>
                <option value="unread">Unread</option>
                <option value="read">Read</option>
              </select>
            </div>
          </div>
        </div>

        <div className="p-4 md:p-6">
          {isLoading ? (
            <div className="flex items-center justify-center py-12">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-violet-500" />
            </div>
          ) : filteredNotifications?.length > 0 ? (
            <div className="space-y-3">
              {filteredNotifications.map((notification, index) => (
                <NotificationItem key={index} notification={notification} />
              ))}
            </div>
          ) : (
            <div className="text-center py-12">
              <Bell className="h-12 w-12 text-gray-300 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                No notifications
              </h3>
              <p className="text-gray-500">
                {searchQuery
                  ? `No notifications matching "${searchQuery}"`
                  : filter === "all"
                  ? "You're all caught up!"
                  : `No ${filter} notifications`}
              </p>
            </div>
          )}
        </div>
      </div>
    </MainLayout>
  );
};

export default ShowAllNotification;
