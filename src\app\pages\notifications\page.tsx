"use client";
import React, { useEffect, useState, useCallback } from "react";
import {
  Bell,
  Search,
  RefreshCw,
  <PERSON>,
  Dot
} from "lucide-react";
import { UseNotification } from "@/hooks/useNotification";
import { KEYS } from "@/lib/keys";
import { getLocalStorageItem } from "@/lib/utils";
import { NotificationResponse } from "@/types";
import MainLayout from "../layouts/mainLayout";
import { useTranslation } from "next-i18next";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
const NotificationItem = ({
  notification,
}: {
  notification: NotificationResponse;
}) => {
  const getTimeAgo = (dateString: string) => {
    const now = new Date();
    const past = new Date(dateString);
    const diffInSeconds = Math.floor((now.getTime() - past.getTime()) / 1000);

    if (diffInSeconds < 60) return `${diffInSeconds} seconds ago`;
    if (diffInSeconds < 3600)
      return `${Math.floor(diffInSeconds / 60)} minutes ago`;
    if (diffInSeconds < 86400)
      return `${Math.floor(diffInSeconds / 3600)} hours ago`;
    if (diffInSeconds < 604800)
      return `${Math.floor(diffInSeconds / 86400)} days ago`;
    return past.toLocaleDateString();
  };

  const getNotificationIcon = () => {
    // You can customize this based on notification type if available
    const iconClass = "h-5 w-5";
    return <Bell className={iconClass} />;
  };

  const getNotificationStyle = () => {
    if (!notification.is_read) {
      return {
        card: "bg-gradient-to-r from-blue-50 to-indigo-50 border-l-4 border-blue-500",
        icon: "bg-blue-100 text-blue-600",
        badge: "bg-blue-100 text-blue-800"
      };
    }
    return {
      card: "bg-white border-l-4 border-gray-200",
      icon: "bg-gray-100 text-gray-500",
      badge: "bg-gray-100 text-gray-600"
    };
  };

  const { t } = useTranslation("common");
  const styles = getNotificationStyle();

  return (
    <Card className={`group hover:shadow-lg transition-all duration-300 cursor-pointer ${styles.card}`}>
      <CardContent className="p-4">
        <div className="flex items-start gap-4">
          <div className="flex-shrink-0">
            <div className={`w-12 h-12 rounded-full flex items-center justify-center ${styles.icon}`}>
              {getNotificationIcon()}
            </div>
          </div>

          <div className="flex-1 min-w-0">
            <div className="flex items-start justify-between gap-2">
              <p className={`font-medium leading-relaxed ${!notification.is_read ? 'text-gray-900' : 'text-gray-700'}`}>
                {notification.messaage_text || "No message content"}
              </p>
              {!notification.is_read && (
                <Dot className="h-6 w-6 text-blue-500 flex-shrink-0 animate-pulse" />
              )}
            </div>

            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mt-3 gap-2">
              <div className="flex items-center gap-2 text-sm text-gray-500">
                <Clock className="h-4 w-4" />
                <span>{getTimeAgo(notification.created_at)}</span>
              </div>

              {!notification.is_read && (
                <span className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium ${styles.badge}`}>
                  {t("New")}
                </span>
              )}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

const ShowAllNotification = () => {
  const [isLoading, setIsLoading] = useState(true);
  const [allMessages, setAllMessages] = useState<NotificationResponse[]>([]);
  const [filter, setFilter] = useState("all");
  const [searchQuery, setSearchQuery] = useState("");
  const { getNotifications } = UseNotification();
  const { t } = useTranslation("common");

  const fetchNotificationData = useCallback(async (): Promise<void> => {
    try {
      let userID = getLocalStorageItem(KEYS.USER_ID) || "";
      const orgID = getLocalStorageItem(KEYS.ORG_ID);
      const response = await getNotifications({
        org_id: orgID as string,
        user_id: userID as string,
      });
      setAllMessages(response);
      setIsLoading(false);
    } catch (error) {
      console.error("Failed to fetch notifications:", error);
      setIsLoading(false);
    }
  }, [getNotifications]);

  useEffect(() => {
    fetchNotificationData();
  }, [fetchNotificationData]);

  const filteredNotifications = allMessages?.filter((notification) => {
    const matchesSearch = notification.messaage_text
      ?.toLowerCase()
      .includes(searchQuery.toLowerCase());
    const matchesFilter =
      filter === "all" ||
      (filter === "unread" && !notification.is_read) ||
      (filter === "read" && notification.is_read);
    return matchesSearch && matchesFilter;
  });

  const unreadCount = allMessages?.filter((msg) => !msg.is_read).length;

  return (
    <MainLayout titleText={""}>
      <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100">
        {/* Enhanced Header */}
        <div className="bg-white border-b shadow-sm sticky top-0 z-10">
          <div className="max-w-4xl mx-auto px-4 py-4 md:px-6 md:py-6">
            {/* Title and Stats */}
            <div className="flex items-center justify-between mb-6">
              <div className="flex items-center gap-4">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-blue-100 rounded-lg">
                    <Bell className="h-6 w-6 text-blue-600" />
                  </div>
                  <div>
                    <h1 className="text-2xl font-bold text-gray-900">{t("Notifications")}</h1>
                    <p className="text-sm text-gray-500">
                      {allMessages?.length || 0} {t("total notifications")}
                    </p>
                  </div>
                </div>
                {unreadCount > 0 && (
                  <div className="flex items-center gap-2 px-3 py-2 bg-blue-50 border border-blue-200 rounded-lg">
                    <Dot className="h-4 w-4 text-blue-500 animate-pulse" />
                    <span className="text-sm font-medium text-blue-700">
                      {unreadCount} {t("new")}
                    </span>
                  </div>
                )}
              </div>

              <Button
                onClick={() => fetchNotificationData()}
                variant="outline"
                size="sm"
                className="flex items-center gap-2"
                disabled={isLoading}
              >
                <RefreshCw className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
                {t("Refresh")}
              </Button>
            </div>

            {/* Search and Filter */}
            <div className="flex flex-col gap-4 sm:flex-row sm:gap-4">
              <div className="flex-1 relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                <input
                  type="text"
                  placeholder={t("Search notifications...")}
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full pl-10 pr-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                />
              </div>
              <select
                value={filter}
                onChange={(e) => setFilter(e.target.value)}
                className="px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white min-w-[120px] transition-all duration-200"
              >
                <option value="all">{t("All")}</option>
                <option value="unread">{t("Unread")}</option>
                <option value="read">{t("Read")}</option>
              </select>
            </div>
          </div>
        </div>

        {/* Content Area */}
        <div className="max-w-4xl mx-auto p-4 md:p-6">
          {isLoading ? (
            <div className="flex flex-col items-center justify-center py-16">
              <div className="relative">
                <div className="animate-spin rounded-full h-12 w-12 border-4 border-blue-200 border-t-blue-600" />
                <Bell className="absolute inset-0 m-auto h-6 w-6 text-blue-600" />
              </div>
              <p className="mt-4 text-gray-600 font-medium">{t("Loading notifications...")}</p>
            </div>
          ) : filteredNotifications?.length > 0 ? (
            <div className="space-y-4">
              {filteredNotifications.map((notification, index) => (
                <NotificationItem key={index} notification={notification} />
              ))}

              {/* Load more button if needed */}
              {filteredNotifications.length >= 10 && (
                <div className="flex justify-center pt-6">
                  <Button variant="outline" className="flex items-center gap-2">
                    <RefreshCw className="h-4 w-4" />
                    {t("Load more")}
                  </Button>
                </div>
              )}
            </div>
          ) : (
            <div className="text-center py-16">
              <div className="relative inline-block">
                <div className="w-20 h-20 bg-gray-100 rounded-full flex items-center justify-center mb-6">
                  <Bell className="h-10 w-10 text-gray-400" />
                </div>
                {searchQuery && (
                  <div className="absolute -top-2 -right-2 w-8 h-8 bg-orange-100 rounded-full flex items-center justify-center">
                    <Search className="h-4 w-4 text-orange-600" />
                  </div>
                )}
              </div>

              <h3 className="text-xl font-semibold text-gray-900 mb-2">
                {searchQuery ? t("No matching notifications") : t("No notifications")}
              </h3>

              <p className="text-gray-500 max-w-md mx-auto leading-relaxed">
                {searchQuery
                  ? `${t("No notifications found matching")} "${searchQuery}". ${t("Try adjusting your search terms.")}`
                  : filter === "all"
                  ? t("You're all caught up! No new notifications at the moment.")
                  : `${t("No")} ${filter} ${t("notifications found.")}`}
              </p>

              {searchQuery && (
                <Button
                  variant="outline"
                  onClick={() => setSearchQuery("")}
                  className="mt-4"
                >
                  {t("Clear search")}
                </Button>
              )}
            </div>
          )}
        </div>
      </div>
    </MainLayout>
  );
};

export default ShowAllNotification;
