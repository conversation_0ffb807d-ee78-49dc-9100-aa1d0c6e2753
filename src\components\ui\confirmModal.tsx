import React from "react";
import { Button } from "@/components/ui/button";
import { useRouter } from "next/navigation";
import { SUCCESS_MESSAGES } from "@/lib/messages";
import { useToast } from "@/components/ui/use-toast";
import { LoginUserData, ToastType } from "@/types";
import { UseLogClass } from "@/hooks/useLog";
import { getLocalStorageItem } from "@/lib/utils";
import { KEYS } from "@/lib/keys";

export default function ComfirmSubmit({
  onCancel,
  onSave,
}: {
  onSave: () => void;
  onCancel: () => void;
  isModal?: boolean;
}): React.JSX.Element {
  const router = useRouter();
  const { toast } = useToast() as ToastType;
  const { insertLogDetails } = UseLogClass();
  const handleLogoutClick = (): void => {
    localStorage.removeItem("profile_image");
    localStorage.removeItem("configurations");
    localStorage.removeItem("courseData");
    localStorage.removeItem("orgId");
    localStorage.removeItem("orgName");
    localStorage.removeItem("profile_image");
    localStorage.removeItem("userId");
    localStorage.removeItem("PROFILE_FNAME");
    localStorage.removeItem("PROFILE_LNAME");
    localStorage.removeItem("PROFILE_PHONE_NUMBER");
    // localStorage.clear();
    router.push("/pages/login");
    toast({
      variant: "default",
      title: SUCCESS_MESSAGES.success,
      description: SUCCESS_MESSAGES.logout,
    });
    const USER_DATA = getLocalStorageItem(KEYS.USER_DETAILS);
    const userInfo = JSON.parse(USER_DATA ?? "{}") as LoginUserData;
    insertLogDetails(
      "Authenticaion",
      "Logout",
      `User Logged out `,
      "SUCCESS",
       userInfo.id
    );
    localStorage.removeItem("userDetails");
    localStorage.removeItem("access_token");
    onSave();
  };

  return (
    
 <div className="rounded p-4 pt-0">
  <div className="mb-2 text-center text-lg">
    <p>Would you like to log out of the session?</p>
  </div>
  <div className="flex justify-center items-center space-x-6 mt-4"> 
    <Button
      type="button"
      variant="outline"
      className="primary w-24 h-10 rounded-3xl"
      onClick={onCancel}
    >
      No
    </Button>
    <Button
      type="submit"
      className=" w-24 h-10 rounded-3xl" 
      onClick={handleLogoutClick}
    >
      Yes
    </Button>
  </div>
</div>


    
  );
}
