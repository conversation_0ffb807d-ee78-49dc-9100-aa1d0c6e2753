import { SubjectProgressType } from "@/types";
import React, { useEffect, useState } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "../ui/card";
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  Tooltip,
  Legend,
  XAxis,
  <PERSON>Axis,
  ResponsiveContainer,
} from "recharts";
import { Spinner } from "../ui/progressiveLoder";

interface subjectProgressInterface {
  SubjectProgress: SubjectProgressType[];
}

const SubjectsProgress = ({ SubjectProgress }: subjectProgressInterface) => {
  const [isLoading, setIsLoading] = useState(true);

  // Safely process data
  const processedData =
  SubjectProgress?.map((item) => {
    const completed = Math.min(item.completed, 100).toFixed(2);
    const ongoing =
    item.ongoing > 0 && item.ongoing < 100
      ? parseFloat(Math.min(item.ongoing, 100).toFixed(2))
      : 0;
    // const notStarted = 100 - Math.min(completed + ongoing, 100);

    return {
      ...item,
      completed,
      ongoing,
      // notStarted,
    };
  }) || [];

  useEffect(() => {
    setIsLoading(false);
  }, [JSON.stringify(SubjectProgress)]);

  const hasData = processedData.length > 0;

  return (
    <>
      {!isLoading ? (
       
           <Card className="bg-white border border-gray-200 rounded-lg w-full shadow-md">
             <div className="bg-purple-600 h-2 w-full rounded-t-lg"></div>
        
            <CardHeader className="bg-purple-100">
              <CardTitle className="text-lg font-semibold text-purple-700">
                Progress
              </CardTitle>
            </CardHeader>
            <CardContent>
              {hasData ? (
                <ResponsiveContainer width="100%" height={290}>
                  <BarChart
                  data={processedData.slice(0, 5)}
                  layout="vertical"
                  width={300}
                  height={250}
                  barSize={20}
                >
                  <XAxis
                    type="number"
                    domain={[0, 100]}
                    stroke="#6C757D"
                    fontSize="14px"
                  />
                  <YAxis
                    dataKey="subject"
                    type="category"
                    width={150}
                    stroke="#495057"
                    fontSize="14px"
                  />
                  <Tooltip  cursor={{ fill: "#fff" }}/>
                  <Legend
                    iconType="square"
                    verticalAlign="bottom"
                    align="center"
                    layout="horizontal"
                    wrapperStyle={{ fontSize: "12px" }}
                    iconSize={11} 
                    className="text-xs font-normal text-gray-600"
                  />
                  <Bar
                    fontSize="12px"
                    dataKey="completed"
                    stackId="a"
                    fill="#28A745"
                    name="Completed"
                    animationDuration={1500}
                    animationBegin={200}
                  />
                   <Bar
                    fontSize="12px"
                    dataKey="ongoing"
                    stackId="a"
                    fill="#F97316"
                    name="Ongoing"
                    animationDuration={1500}
                  />
                 
                </BarChart>
                  
               
                </ResponsiveContainer>
              ) : (
                <div className="flex flex-col items-center justify-center h-[290px]">
                  <p className="text-gray-500 text-lg font-medium pb-10">No data available!</p>
                </div>
              )}
            </CardContent>
          </Card>
        
      ) : (
        <Spinner />
      )}
    </>
  );
};

export default SubjectsProgress;
