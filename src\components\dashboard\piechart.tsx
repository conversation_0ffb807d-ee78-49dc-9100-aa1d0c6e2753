import { FC } from 'react';
import { Pie } from 'react-chartjs-2';
import {CategoryScale, Chart, registerables} from 'chart.js'; 
Chart.register(CategoryScale);
Chart.register(...registerables);

const PieChart: FC = () => {
  const data = {
    labels: ['Completed', 'In Progress', 'Not Started'],
    datasets: [
      {
        data: [30, 50, 20],
        backgroundColor: ['rgba(75, 192, 192, 0.6)', 'rgba(255, 206, 86, 0.6)', 'rgba(255, 99, 132, 0.6)'],
      },
    ],
  };

  return (
    <div className="p-4 bg-white rounded shadow">
      <h3 className="text-lg font-semibold mb-4">Progress Breakdown</h3>
      <Pie data={data} />
    </div>
  );
};

export default PieChart;
