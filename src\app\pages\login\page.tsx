"use client";
import React, { useEffect, useState } from "react";
import "../../../styles/auth.css";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import Link from "next/link";
import AuthLayout from "../layouts/authlayout";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormMessage,
} from "@/components/ui/form";
import {
  ErrorCatch,
  LoginFormType,
  SessionData,
  ToastType,
  LoginUserData,
  insertNotificationRequest,
  TopicsData,
  CourseData,
} from "@/types";
import { LoginFormSchema } from "@/schema/schema";
import { useRouter } from "next/navigation";
import useAuthorization from "@/hooks/useAuth";
import { useToast } from "@/components/ui/use-toast";
import { Spinner } from "@/components/ui/progressiveLoder";
import { ERROR_MESSAGES, SUCCESS_MESSAGES } from "@/lib/messages";
import "../../../styles/main.css";
import useOrganization from "@/hooks/useOrganization";
import { KEYS } from "@/lib/keys";
import useFcmToken from "@/hooks/useFcmToken";
import { UseTopics } from "@/hooks/useTopics";
import { Separator } from "@radix-ui/react-menubar";
import { supabase } from "@/lib/client";
import useCustomBranding from "@/hooks/useCustomBranding";
import { useTheme } from "@/context/ThemeContext";
import { UseLogClass } from "@/hooks/useLog";


export default function LoginAccount(): React.JSX.Element {
  const form = useForm<LoginFormType>({
    resolver: zodResolver(LoginFormSchema),
  });
  const router = useRouter();
  const { signIn, getProfileImage, insertToken, getConfigurationSettings } =
    useAuthorization();
  const { getUserOrganization } = useOrganization();
  const { toast } = useToast() as ToastType;
  const [loading, setLoading] = useState(false);
  const { token } = useFcmToken();
  const { getTopicsData } = UseTopics();
  const { getCustomBrandingDetails } = useCustomBranding();
  const { insertLogDetails } = UseLogClass();
  useEffect(() => {
    document.documentElement.classList.add('light');
    localStorage.removeItem(KEYS.USER_DETAILS);
    localStorage.removeItem(KEYS.PROFILE_IMAGE);
    localStorage.removeItem(KEYS.ACCESS_TOKEN);
    localStorage.removeItem(KEYS.ORG_ID);
    localStorage.removeItem(KEYS.ORG_NAME);
    localStorage.removeItem(KEYS.ORG_DETAILS);
    localStorage.removeItem(KEYS.CURRENT_AFFAIR_CONSTANT);
   

  }, []);
  const getBrandingDetails = async (orgId: string): Promise<void> => {
   
    try {
      const data = await getCustomBrandingDetails(orgId as string);
      if(data != null) {
       localStorage.setItem("brandingDetails", JSON.stringify(data));
      }
      else {
        localStorage.removeItem("brandingDetails");
      }
      
    
    } catch (error) {
      console.log("Branding Details:", error);
    }
  };
  async function onSubmit(data: LoginFormType): Promise<void> {
    setLoading(true);
    try {
      const result = await signIn(data);
      if (result.length > 0 && result[0].data) {
        const userData = result[0].data as LoginUserData;
        const apiData = result[0].session as SessionData;
        const access_token = apiData.access_token;
        const session_data = {
          id: userData.id,
          last_sign_in_at: userData.last_sign_in_at,
          user_metadata: userData.user_metadata,
          email: userData?.email,
        };
        await getUserImage(session_data.id);
        localStorage.setItem(KEYS.ACCESS_TOKEN, access_token);
        localStorage.setItem(KEYS.USER_DETAILS, JSON.stringify(session_data));

        toast({
          variant: "default",
          title: SUCCESS_MESSAGES.success,
          description: SUCCESS_MESSAGES.logIn,
        });
        const userDetails = localStorage.getItem(KEYS.USER_DETAILS);
        if (userDetails !== null && userDetails !== undefined) {
        

          const userInfo = JSON.parse(userDetails);
          const orgResult = await getUserOrganization(userInfo.id);
          // localStorage.setItem(KEYS.ORG_ID, orgResult[0].org_id);
          if (orgResult.length === 1) {
            getConfigData(orgResult[0].org_id)
            localStorage.setItem(KEYS.ORG_ID, orgResult[0].org_id);
            localStorage.setItem(KEYS.ORG_NAME, orgResult[0].org_name);
            localStorage.setItem(KEYS.USER_ID, userInfo.id);
            fetchTopics(orgResult[0].org_id, orgResult[0].org_name);
            sentNotification(orgResult[0].org_id, userInfo.id);
           await insertLogDetails(
             "Authentication",
             "Login",
             "User Logged in  ",
             "SUCCESS",
             userInfo.id
           );
            getBrandingDetails(orgResult[0].org_id);
          } else if (orgResult.length > 1) {
            router.push("/pages/organization");
          } else {
            router.push("/pages/dashboard");
          }
        }
      }
      if (result.length > 0 && result[0].error !== "") {
        
          await insertLogDetails(
             "Authentication",
             "Login",
             "User Failed To Logged in  ",
             "ERROR",
              null
           );
        toast({
          variant: "destructive",
          title: ERROR_MESSAGES.error,
          description: result[0].error,
        });
      }
    } catch (error) {
      const err = error as ErrorCatch;
      toast({
        variant: "destructive",
        title: ERROR_MESSAGES.error,
        description: err?.message,
      });
      await insertLogDetails(
             "Authentication",
             "Login",
             "User Failed To Logged in  ",
             "ERROR",
              null
           );
    } finally {
      setLoading(false);
    }
  }
  const sentNotification = async (orgId: string, userId: string) => {
    const params: insertNotificationRequest = {
      org_id: orgId,
      user_id: userId,
      notification_data: {
        device_token: token,
        status: "active",
      },
    };
    try {
      const resp = await insertToken(params);
    } catch (error) {
      const err = error as ErrorCatch;
      toast({
        variant: "destructive",
        title: ERROR_MESSAGES.error,
        description: err?.message,
      });
    }
  };
  async function getUserImage(data: string): Promise<void> {
    try {
      const image = await getProfileImage(data);
      localStorage.setItem(KEYS.PROFILE_IMAGE, JSON.stringify(image));
    } catch (error) {
      const err = error as ErrorCatch;
      toast({
        variant: "destructive",
        title: ERROR_MESSAGES.error,
        description: err?.message,
      });
    }
  }
  async function getConfigData(org_id: string): Promise<void> {
    try {
      const response = await getConfigurationSettings(org_id);
     console.log('config-resp',response)
     localStorage.setItem('configurations', JSON.stringify(response))
    } catch (error) {
      const err = error as ErrorCatch;
      toast({
        variant: "destructive",
        title: ERROR_MESSAGES.error,
        description: err?.message,
      });
    }
  }
  const fetchTopics = async (orgId: string, orgName: string): Promise<void> => {
    const params = {
      org_id: orgId ?? "",
      filter_data: 0,
    };
    try {
      const response: TopicsData[] = await getTopicsData(params);
      if (response && response.length > 0) {
        if (typeof window !== "undefined") {
          localStorage.setItem(
            "courseData",
            JSON.stringify(
              response.map((item) => item.courses).flat() as CourseData[]
            )
          );
          if (response.length === 1) {
            localStorage.setItem(
              KEYS.COURSE_ID,
              response[0].courses?.[0].course_id as string
            );
          }

          localStorage.setItem(KEYS.ORG_NAME, orgName);
        }
        router.push("/pages/dashboard");
      }
      else {
        router.push("/pages/emptyCourse");
      }
    } catch (error) {
      const err = error as ErrorCatch;
      toast({
        variant: "destructive",
        title: ERROR_MESSAGES.error,
        description: err?.message,
      });
    }
  };
  const gotoAzureLogin = async () => {
    try {
      const { data, error } = await supabase.auth.signInWithOAuth({
        provider: "azure",
        options: {
          scopes: "email",
          redirectTo: process.env.NEXT_PUBLIC_SUPABASE_REDIRECT_URL,
        },
      });

      if (error) {
        console.error("OAuth login error:", error.message);
        return;
      }

      console.log("OAuth login data:", data);
      localStorage.setItem("azureData", JSON.stringify(data));
    } catch (e) {
      console.error("Exception during Azure login:", e);
    }
  };

  return (
    <AuthLayout>
      <div className="lg:p-8 ">
        <div className="mx-auto flex w-full flex-col justify-center space-y-6 sm:w-[350px] login-container border">
          <div className="relative">
            <div className="absolute inset-0 flex items-center">
              <Separator className="w-full"/>
            </div>
            <h2 className="text-2xl font-bold text-center text-gray-900 pt-5">
              Welcome to SmartLearn
            </h2>
            <div className="grid grid-cols-3 gap-4 "></div>
          </div>
          <Form {...form}>
            {/* <h1 className="text-2xl font-bold text-black ml-2 text-center">Login</h1> */}
            <form
              onSubmit={(event) => void form.handleSubmit(onSubmit)(event)}
              className="space-y-6"
            >
              {process.env.NEXT_PUBLIC_EMAIL_LOGIN_ALLOWED === "true" && (
                <div className="space-y-4">
                  <FormField
                    name="email"
                    control={form.control}
                    render={({ field }) => (
                      <FormItem>
                        <FormControl>
                          <Input
                            id="email"
                            type="email"
                            className="w-full rounded-md py-2 px-4 bg-white "
                            placeholder="<EMAIL>"
                            autoCapitalize="none"
                            autoComplete="email"
                            autoCorrect="off"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    name="password"
                    control={form.control}
                    render={({ field }) => (
                      <FormItem>
                        <FormControl>
                          <Input
                            id="password"
                            type="password"
                            className="w-full rounded-md py-2 px-4 bg-white"
                            placeholder="Password"
                            autoCapitalize="none"
                            autoComplete="password"
                            autoCorrect="off"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  {process.env.NEXT_PUBLIC_EMAIL_LOGIN_ALLOWED === "true" && (
                    <div className="px-8 text-center text-sm text-muted-foreground">
                      <p>
                        Forgot password?
                        <Link
                          href="/pages/resetPassword"
                          className="ml-1 underline underline-offset-4 hover:text-primary"
                        >
                          Reset
                        </Link>
                      </p>
                    </div>
                  )}

                  <div className="flex justify-center items-center  azure-container">
                    <Button
                      type="submit"
                      className="w-1/4 "
                      variant="default"
                    >
                      Submit
                    </Button>
                  </div>
                </div>
              )}
              {process.env.NEXT_PUBLIC_AZURE_SIGNIN_ALLOWED === "true" && (
                <div className="relative text-center text-xs uppercase">
                <span className="bg-white px-2 text-muted-foreground inline-block">
                  Sign in with
                </span>
              
                <div className="mt-4 grid grid-cols-1 gap-2">
                  <Button
                    type="button"
                    variant="default"
                    className="w-full bg-white hover:bg-gray-50 text-black border border-gray-200 flex items-center justify-center space-x-2"
                    onClick={() => {
                      gotoAzureLogin();
                    }}
                  >
                    <svg
                      className="h-6 w-6"
                      xmlns="http://www.w3.org/2000/svg"
                      viewBox="0 0 48 48"
                    >
                      <path fill="#f25022" d="M13 13h10v10H13z" />
                      <path fill="#00a4ef" d="M25 13h10v10H25z" />
                      <path fill="#7fba00" d="M13 25h10v10H13z" />
                      <path fill="#ffb900" d="M25 25h10v10H25z" />
                    </svg>
                    <span>Citrus Microsoft ID</span>
                  </Button>
                </div>
              </div>
              
              )}

              {process.env.NEXT_PUBLIC_EMAIL_LOGIN_ALLOWED === "true" && (
                <div>
                  <div className="px-8 text-center text-sm text-muted-foreground">
                    <p>
                      Don&apos;t have an account?
                      <Link
                        href="/pages/signup"
                        className="ml-1 underline underline-offset-4 hover:text-primary"
                      >
                        Sign Up
                      </Link>
                    </p>
                  </div>
                </div>
              )}
            </form>
          </Form>
          {loading && <Spinner></Spinner>}
        </div>
      </div>
    </AuthLayout>
  );
}
