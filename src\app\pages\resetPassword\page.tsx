"use client";
import React, { useState } from "react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import AuthLayout from "../layouts/authlayout";
import Link from "next/link";
import { zod<PERSON>esolver } from "@hookform/resolvers/zod";
import { ForgotPasswordFormSchema } from "@/schema/schema";
import { ErrorCatch, ForgotPasswordFormType } from "@/types";
import { useForm } from "react-hook-form";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormMessage,
} from "@/components/ui/form";
import { Spinner } from "@/components/ui/progressiveLoder";
import { toast } from "@/components/ui/use-toast";
import { ERROR_MESSAGES, SUCCESS_MESSAGES } from "@/lib/messages";
import useAuthorization from "@/hooks/useAuth";
import "../../../styles/main.css";

export default function ResetPassword(): React.JSX.Element {
  const form = useForm<ForgotPasswordFormType>({
    resolver: zodResolver(ForgotPasswordFormSchema),
  });

  const [loading, setLoading] = useState(false);
  const { sendPasswordResetMail } = useAuthorization();

  async function onSubmit(data: ForgotPasswordFormType): Promise<void> {
    //setLoading(true);
    try {
      const result = await sendPasswordResetMail(data.email);
      if (result) {
        toast({
          variant: "default",
          title: SUCCESS_MESSAGES.title,
          description: SUCCESS_MESSAGES.reset_link_sent_success,
        });
      }
    } catch (error) {
      const err = error as ErrorCatch;
      toast({
        variant: "destructive",
        title: ERROR_MESSAGES.error,
        description: err?.message,
      });
    } finally {
      setLoading(false);
    }
  }

  return (
    <AuthLayout>
      <div className="flex flex-col items-center p-4 space-y-4 bg-transparent">
        <Form {...form}>
       
          <form
            onSubmit={(event) => void form.handleSubmit(onSubmit)(event)}
            className="grid gap-4"
          >
            <div className="bg-white shadow-md rounded-lg p-6 w-full max-w-md">
            <h1 className="text-2xl font-bold mb-2 text-center">Forgot Password</h1>
              <div className="text-center">
                <p className="text-gray-600 mb-4">
                  Provide your email and we will send you a link to reset your
                  password
                </p>
              </div>
              <FormField
                name="email"
                control={form.control}
                render={({ field }) => (
                  <FormItem>
                    <FormControl>
                      <Input
                        id="email"
                        type="email"
                        className="w-full rounded-md py-2 px-4"
                        placeholder="Enter your email"
                        autoCapitalize="none"
                        autoComplete="email"
                        autoCorrect="off"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <div className="mt-6">
                <Button className="w-full">Submit</Button>
              </div>
              <div className="px-8 text-center text-sm text-muted-foreground mt-5">
                <p>
                  <Link
                    href="/pages/login"
                    className="ml-1 underline underline-offset-4 hover:text-primary"
                  >
                    Login
                  </Link>
                </p>
              </div>
            </div>
          </form>
        </Form>
      </div>
      {loading && <Spinner></Spinner>}
    </AuthLayout>
  );
}
