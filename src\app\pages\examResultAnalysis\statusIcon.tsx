"use client";

import React from "react";
import { ClipboardX, <PERSON><PERSON>he<PERSON> } from "lucide-react";
import { useTranslation } from "react-i18next";
export default function StatusIcon({
  status,
}: {
  status: string;
}): React.JSX.Element {
  const { t } = useTranslation();
  return (
    <>
      <div className="flex justify-center">
        {status === "Passed" && (
          <div className="flex flex-col items-center">
            <div className="w-28 h-28 bg-emerald-100 rounded-full flex justify-center items-center">
              <ShieldCheck className="text-[#00A642] w-20 h-20" />
            </div>
            <div className="text-[#00A642] mt-4 font-bold text-2xl">
              {t("You have passed the examination")}
            </div>
          </div>
        )}

        {status === "Failed" && (
          <div className="flex flex-col items-center">
            <div className="w-28 h-28 bg-red-100 rounded-full flex justify-center items-center">
              <ClipboardX className="text-[#FF2B2B] w-20 h-20" />
            </div>
            <div className="text-[#FF2B2B] mt-4 font-bold text-2xl">
              {t("You failed in the examination")}
            </div>
          </div>
        )}
      </div>
    </>
  );
}
