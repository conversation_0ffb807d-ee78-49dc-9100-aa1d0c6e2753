"use client";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  convertDecimalHoursToHrMin,
  convertTimeStringToHours,
} from "@/lib/timeUtils";
import { CourseStats } from "@/types";
import React, { useEffect } from "react";
import { ArrowR<PERSON>, Eye } from "lucide-react";
import { useRouter } from "next/navigation";
import { KEYS } from "@/lib/keys";
import { useTranslation } from "next-i18next";

interface CourseStatsProps {
  CourseStatistics: CourseStats[];
  closeDialog: () => void;
  CourseId: string | null;
  isMain?: boolean;
  title?: string;
}

export default function CourseDetailsModal({
  CourseStatistics,
  closeDialog,
  CourseId,
  isMain,
  title,
}: CourseStatsProps): React.JSX.Element {
  const { t } = useTranslation("common");
  const router = useRouter();
  useEffect(() => {
    console.log("CourseStatistics", CourseStatistics);
  }, []);
  return (
    <div>
      <div>
        <h1 className="text-2xl font-semibold mb-4">{t(title || "")}</h1>
      </div>

      <div className="overflow-x-auto">
        {CourseStatistics?.length > 0 ? (
          <table className="w-full border border-gray-200 rounded-md overflow-hidden">
            <thead className="bg-gray-100">
              <tr>
                <th className="text-left px-4 py-2 font-semibold">
                  {isMain ? "Course Name" : "Resource Name"}
                </th>
                {!isMain && CourseStatistics?.[0]?.valuetype != "Value" && (
                  <th className=" px-4 py-2 font-semibold">{"Section Name"}</th>
                )}
                <th className="text-right px-4 py-2 font-semibold">
                  {CourseStatistics?.[0]?.valuetype}
                </th>
                {(title === "Total Course" || title === "Completed Course") && (
                  <th> {t("View course details")}</th>
                )}
              </tr>
            </thead>
            <tbody>
              {CourseStatistics?.map((activity, index) => (
                <tr
                  key={index}
                  className={index % 2 === 0 ? "bg-white" : "bg-gray-50"}
                >
                  {/* Course/Resource Name */}
                  <td className="px-4 py-2 text-small">
                    {activity?.course_name}
                  </td>
                  {!isMain && CourseStatistics?.[0]?.valuetype != "Value" && (
                    <th className="px-4 py-2 text-small font-normal">
                      {activity?.section_name}
                    </th>
                  )}
                  {/* Value */}
                  <td className="px-4 py-2 text-right text-small">
                    {CourseStatistics?.[0]?.valuetype === "Time Spent" ? (
                      <span>
                        {convertTimeStringToHours(activity.value?.toString())}
                      </span>
                    ) : CourseStatistics?.[0]?.valuetype === "Progress" ? (
                      <span>
                        {activity.value === 0
                          ? 0
                          : activity.value > 100
                          ? 100
                          : activity.value?.toFixed(2)}
                      </span>
                    ) : CourseStatistics?.[0]?.valuetype === "Achievements" ? (
                      <span>{activity.value}</span>
                    ) : (
                      <span>{activity.value?.toFixed(2)}</span>
                    )}
                  </td>
                  {(title === "Total Course" ||
                    title === "Completed Course") && (
                    <td>
                      <div className="flex justify-center">
                        <ArrowRight
                          className="w-5 h-5 text-primary cursor-pointer"
                          style={{
                            background: "none",
                            border: "none",
                            boxShadow: "none",
                          }}
                          onClick={() => {
                            localStorage.setItem(
                              KEYS.COURSE_ID,
                              activity.course_id as string
                            );
                            if (
                              process.env.NEXT_PUBLIC_SUBJECT_WISE_LIST ===
                              "true"
                            ) {
                              router.push(`/pages/section-details`);
                            } else {
                              router.push(
                                `/pages/course-details?course_id=${activity.course_id}`
                              );
                            }
                          }}
                        />
                      </div>
                    </td>
                  )}
                </tr>
              ))}
            </tbody>
          </table>
        ) : (
          <div className="text-center text-gray-500 py-8">
            {t("No data available")}
          </div>
        )}
      </div>

      <div className="flex justify-end mt-4">
        <Button variant="outline" className="rounded-md" onClick={closeDialog}>
          {t("Close")}
        </Button>
      </div>
    </div>
  );
}
