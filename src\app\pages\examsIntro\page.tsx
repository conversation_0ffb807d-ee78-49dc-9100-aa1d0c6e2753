"use client";

import { <PERSON><PERSON><PERSON><PERSON>, Clock12, <PERSON><PERSON><PERSON>2, FileSearch } from "lucide-react";
import MainLayout from "../layouts/mainLayout";
import "../../../styles/main.css";
import { Button } from "@/components/ui/button";
import { useRouter, useSearchParams } from "next/navigation";
import { useEffect, useState } from "react";
import { useExam } from "@/hooks/useExam";
import { ExamQuestionType, ExamQuestionsResult, ExamViewRequest, InnerItem, LoginUserData } from "@/types";
import moment from "moment";
import { Spinner } from "@/components/ui/progressiveLoder";
import { DATE_FORMAT } from "@/lib/constants";
import ExamModal from "./exam-modal";
import { KEYS } from "@/lib/keys";
import NextBreadcrumb from "@/components/breadcrumb";
import getBreadCrumbItems from "@/hooks/useBreadcrumbs";
import { UseLogClass } from "@/hooks/useLog";

export default function ExamsIntro(): React.JSX.Element {
  const router = useRouter();
  const [introData, setIntroData] = useState<ExamQuestionsResult[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [examModal, setExamModal] = useState<boolean>(false);
  const [examModalMsg, setExamModalMsg] = useState("");
  const startExams = () => {
    startNewExam()
    localStorage.removeItem(KEYS.REMAINING_TIME)
  };
  const searchParams = useSearchParams();
  const quizId = searchParams?.get("quiz_id") as string;
  const courseId = searchParams?.get("course_id") as string;
  const orgID = localStorage.getItem(KEYS.ORG_ID);
  const { getExamIntro,startExam } = useExam();
  const { insertLogDetails } = UseLogClass();
  const [breadcrumbItems, setBreadcrumbItems] = useState<InnerItem[]>([]);
  useEffect(() => {
    getExamDetails();

    setBreadcrumbItems(
      getBreadCrumbItems("Exam Intro", {
        course_id: courseId
       
      }),
    );
  }, []);
  const getExamDetails = async (): Promise<void> => {
    try {
      const courseData = await getExamIntro(quizId);
      setIntroData(courseData);

      setIsLoading(false);
    } catch (error) {
      setIsLoading(false);
    }
  };
  const startNewExam = async (): Promise<void> => {
    try {
      const userDetails = localStorage.getItem("userDetails");
      if (userDetails !== null && userDetails !== undefined) {
        const userInfo = JSON.parse(userDetails) as LoginUserData;
        const requestBody: ExamViewRequest = {
          org_id: orgID ?? "",
          quiz_id: quizId,
          user_id: userInfo.id,
          user_start_time: new Date(),
        };
        const result = await startExam(requestBody);
        if(result){
          router.push(`/pages/exam-view?quiz_id=${quizId}&quiz_attempt_id=${result.quiz_attempt_id}&course_id=${courseId}`);
        }
         insertLogDetails(
           "Exam",
           "Exam",
           `Exam started `,
           "SUCCESS",
            quizId
         );
        setIsLoading(false)
        // getQuestionDetails();
      } else {
      }
    } catch (error:any) {
      setExamModal(true)
      setExamModalMsg(error as string)
      setIsLoading(false);
       insertLogDetails(
           "Exam",
           "Exam",
           `Failed to start exam `,
           "ERROR",
            quizId
         );
    }
  };
  const closeModal = (): void =>{
    router.push(`/pages/exams-list?course_id=${courseId}`)
  }

  return (
    <MainLayout titleText={""}>
      <NextBreadcrumb
        items={breadcrumbItems}
        separator={<span> | </span>}
        containerClasses="flex py-5"
        listClasses="hover:underline mx-2 font-bold"
        capitalizeLinks
      />
      {isLoading ? (
        <div className="flex justify-center items-center h-screen">
          <Spinner></Spinner>
        </div>
      ) : (
        <div>
          <div className="flex flex-col ml-4 mr-4">
            <div className="w-full p-3">
              <div className="grid grid-cols-12 gap-6 md:gap-4">
                <div className="col-span-1 flex justify-center">
                  <AlarmClock className="min-w-14 min-h-14 text-[var(--color-font-color)] justify-self-center h-14 p-1 border border-[#FDB666] rounded-lg" />
                </div>
                <div className="col-span-11 pl-4 md:pl-2">
                  <p className="font-semibold text-base">
                   Duration
                  </p>
                  <p className="font-normal text-sm text-[var(--color-font-color)]">
                    {"Attend "}
                    <span className="font-bold ">
                      {introData[0]?.num_of_questions}
                    </span>
                    {" question within "}
                    <span className="font-bold">{introData[0]?.duration}</span>
                    {" minutes"}
                  </p>
                </div>
              </div>
            </div>
            <hr className="w-full" />

            <div className="w-full p-3">
              <div className="grid grid-cols-12 gap-6 md:gap-4">
                <div className="col-span-1 flex justify-center">
                  <Clock12 className="min-w-14 min-h-14 text-[var(--color-font-color)] justify-self-center h-14 p-1 border border-[#FDB666] rounded-lg " />
                </div>
                <div className="col-span-11 pl-4 md:pl-2">
                  <p className="font-semibold text-base">
                    Mark
                  </p>
                  <p className="font-normal text-sm text-[var(--color-font-color)]">
                    <span className="font-bold">
                      {introData[0]?.total_mark}
                    </span>
                    {" is the total mark and "}
                    <span className="font-bold">{introData[0]?.pass_mark}</span>
                    {" is the pass mark"}
                  </p>
                </div>
              </div>
            </div>
            <hr className="w-full" />

            <div className="w-full p-3">
              <div className="grid grid-cols-12 gap-6 md:gap-4">
                <div className="col-span-1 flex justify-center">
                  <FileSearch className="min-w-14 min-h-14 text-[var(--color-font-color)] justify-self-center h-14 p-1 border border-[#FDB666] rounded-lg" />
                </div>
                <div className="col-span-11 pl-4 md:pl-2">
                  <p className="font-semibold text-base">
                  Exam Available in
                  </p>
                  <p className="font-normal text-sm text-[var(--color-font-color)]">
                    {"From "}
                    <span className="font-bold">
                      {moment
                        .utc(introData[0]?.start_time)
                        .local()
                        .format(DATE_FORMAT)}
                    </span>
                    {" to "}
                    <span className="font-bold">
                      {moment
                        .utc(introData[0]?.end_time)
                        .local()
                        .format(DATE_FORMAT)}
                    </span>
                  </p>
                </div>
              </div>
            </div>
            <hr className="w-full" />

            <div className="w-full p-3">
              <div className="grid grid-cols-12 gap-6 md:gap-4">
                <div className="col-span-1 flex justify-center">
                  <FilePlus2 className="min-w-14 min-h-14 text-[var(--color-font-color)] justify-self-center h-14 p-1 border border-[#FDB666] rounded-lg" />
                </div>
                <div className="col-span-11 pl-4 md:pl-2">
                  <p className="font-semibold text-base">
                  Exam Tips
                  </p>
                  <p className="font-normal text-sm text-[var(--color-font-color)]">
                  Rules to follow while attending exam:
                  </p>

                  <p
                    className="font-normal text-sm text-[var(--color-font-color)]"
                    dangerouslySetInnerHTML={{
                      __html: introData[0]?.description,
                    }}
                  />
                </div>
              </div>
            </div>
          </div>
          <div className="w-full grid justify-center p-10">
            <Button
              className="space-x-2  rounded-lg min-w-[350px] md:w-96 h-12"
              onClick={startExams}
              variant="default"
            >
              <p className="text-2xl">Start</p>

            </Button>
          </div>
        </div>
      )}
       {examModal && (
          <ExamModal status={examModalMsg} closeDialog={() => closeModal()} />
        )}
    </MainLayout>
  );
}
